<?php
    include '../db.php';
    session_start();
    
    // Verifica se o usuário está logado e é admin
    if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
        header("Location: ../login.php");
        exit;
    }
    
    // Processar ações (aceitar, rejeitar, concluir pedidos)
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        $pedido_id = $_POST['pedido_id'];
        $action = $_POST['action'];
        
        try {
            switch($action) {
                case 'aceitar':
                    $stmt = $pdo->prepare("UPDATE pedidos SET status = 'Aceite' WHERE id_pedido = ?");
                    $stmt->execute([$pedido_id]);
                    $message = "Pedido aceite com sucesso!";
                    break;
                    
                case 'rejeitar':
                    $stmt = $pdo->prepare("UPDATE pedidos SET status = 'Rejeitado' WHERE id_pedido = ?");
                    $stmt->execute([$pedido_id]);
                    $message = "Pedido rejeitado.";
                    break;
                    
                case 'concluir':
                    $stmt = $pdo->prepare("UPDATE pedidos SET status = 'Concluído' WHERE id_pedido = ?");
                    $stmt->execute([$pedido_id]);
                    $message = "Pedido marcado como concluído!";
                    break;
            }
        } catch (PDOException $e) {
            $error = "Erro ao processar pedido: " . $e->getMessage();
        }
    }
    
    // Buscar pedidos com informações do cliente e bolo
    $status_filter = $_GET['status'] ?? 'todos';
    $sql = "SELECT p.*, u.nome as cliente_nome, u.email as cliente_email,
                   tb.nome_tipo as bolo_nome, p.preco_total as bolo_preco
            FROM pedidos p
            JOIN utilizadores u ON p.id_utilizador = u.id_utilizador
            JOIN bolo b ON p.id_bolo = b.id_bolo
            JOIN tipo_bolo tb ON b.id_tipo = tb.id_tipo";
    
    if ($status_filter !== 'todos') {
        $sql .= " WHERE p.status = ?";
        $stmt = $pdo->prepare($sql . " ORDER BY p.data_pedido DESC");
        $stmt->execute([$status_filter]);
    } else {
        $stmt = $pdo->prepare($sql . " ORDER BY p.data_pedido DESC");
        $stmt->execute();
    }
    
    $pedidos = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Pedidos - Cake Garden</title>
    <link rel="stylesheet" href="../../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .pedidos-container { padding: 20px; }
        .filters { margin-bottom: 20px; display: flex; gap: 10px; align-items: center; }
        .filter-btn { padding: 8px 16px; border: 1px solid #93622B; background: white; color: #93622B; text-decoration: none; border-radius: 5px; transition: all 0.3s; }
        .filter-btn.active, .filter-btn:hover { background: #93622B; color: white; }
        .pedido-card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-left: 4px solid #93622B; }
        .pedido-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .pedido-info { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px; }
        .status-badge { padding: 5px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase; }
        .status-pendente { background: #fff3cd; color: #856404; }
        .status-aceite { background: #d4edda; color: #155724; }
        .status-rejeitado { background: #f8d7da; color: #721c24; }
        .status-concluido { background: #d1ecf1; color: #0c5460; }
        .payment-status { font-size: 0.8em; margin-top: 5px; }
        .payment-pago { color: #28a745; }
        .payment-pendente { color: #ffc107; }
        .payment-falhado { color: #dc3545; }
        .action-buttons { display: flex; gap: 10px; margin-top: 15px; }
        .btn-action { padding: 8px 16px; border: none; border-radius: 5px; cursor: pointer; font-size: 12px; transition: all 0.3s; }
        .btn-aceitar { background: #28a745; color: white; }
        .btn-rejeitar { background: #dc3545; color: white; }
        .btn-concluir { background: #007bff; color: white; }
        .btn-action:hover { opacity: 0.8; }
        .message { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .message.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="sidebar">
            <div class="logo">
                <h2>Cake Garden</h2>
                <p>Painel Admin</p>
            </div>
            <ul class="menu">
                <li><a href="dashboard.php"><i class="fa-solid fa-gauge"></i> Dashboard</a></li>
                <li><a href="bolos.php"><i class="fa-solid fa-cake-candles"></i> Gerir Bolos</a></li>
                <li class="active"><a href="pedidos.php"><i class="fa-solid fa-shopping-cart"></i> Pedidos</a></li>
                <li><a href="clientes.php"><i class="fa-solid fa-users"></i> Clientes</a></li>
                <li><a href="../index.php"><i class="fa-solid fa-home"></i> Ver Site</a></li>
                <li><a href="../logout.php"><i class="fa-solid fa-sign-out-alt"></i> Sair</a></li>
            </ul>
        </div>
        <div class="main-content">
            <div class="header">
                <h1>Gestão de Pedidos</h1>
                <div class="user-info">
                    <span>Bem-vindo, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                    <a href="../logout.php" class="logout-btn"><i class="fa-solid fa-sign-out-alt"></i></a>
                </div>
            </div>
            <div class="pedidos-container">
                <?php if (isset($message)): ?>
                    <div class="message success"><i class="fas fa-check-circle"></i> <?php echo $message; ?></div>
                <?php endif; ?>
                <?php if (isset($error)): ?>
                    <div class="message error"><i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?></div>
                <?php endif; ?>
                <div class="filters">
                    <strong>Filtrar por status:</strong>
                    <a href="?status=todos" class="filter-btn <?php echo $status_filter === 'todos' ? 'active' : ''; ?>">Todos</a>
                    <a href="?status=Pendente" class="filter-btn <?php echo $status_filter === 'Pendente' ? 'active' : ''; ?>">Pendentes</a>
                    <a href="?status=Aceite" class="filter-btn <?php echo $status_filter === 'Aceite' ? 'active' : ''; ?>">Aceites</a>
                    <a href="?status=Concluído" class="filter-btn <?php echo $status_filter === 'Concluído' ? 'active' : ''; ?>">Concluídos</a>
                    <a href="?status=Rejeitado" class="filter-btn <?php echo $status_filter === 'Rejeitado' ? 'active' : ''; ?>">Rejeitados</a>
                </div>
                <?php if (empty($pedidos)): ?>
                    <div class="pedido-card">
                        <p style="text-align: center; color: #666; margin: 0;"><i class="fas fa-inbox"></i><br>Nenhum pedido encontrado.</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($pedidos as $pedido): ?>
                        <div class="pedido-card">
                            <div class="pedido-header">
                                <h3>Pedido #<?php echo $pedido['id_pedido']; ?></h3>
                                <span class="status-badge status-<?php echo strtolower(str_replace(['ã', 'í'], ['a', 'i'], $pedido['status'])); ?>">
                                    <?php echo $pedido['status']; ?>
                                </span>
                            </div>
                            <div class="pedido-info">
                                <div><strong><i class="fas fa-user"></i> Cliente:</strong><br><?php echo htmlspecialchars($pedido['cliente_nome']); ?><br><small><?php echo htmlspecialchars($pedido['cliente_email']); ?></small></div>
                                <div>
                                    <strong><i class="fas fa-cake-candles"></i> Bolo:</strong><br>
                                    <?php echo htmlspecialchars($pedido['bolo_nome']); ?><br>
                                    <small>€<?php echo number_format($pedido['bolo_preco'], 2, ',', '.'); ?></small>
                                    <?php if (isset($pedido['status_pagamento'])): ?>
                                        <div class="payment-status payment-<?php echo strtolower($pedido['status_pagamento']); ?>">
                                            <i class="fas fa-credit-card"></i> <?php echo $pedido['status_pagamento']; ?>
                                            <?php if ($pedido['metodo_pagamento']): ?>
                                                (<?php echo ucfirst($pedido['metodo_pagamento']); ?>)
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div><strong><i class="fas fa-calendar"></i> Data do Pedido:</strong><br><?php echo date('d/m/Y H:i', strtotime($pedido['data_pedido'])); ?></div>
                                <div><strong><i class="fas fa-truck"></i> Data de Entrega:</strong><br><?php echo $pedido['data_entrega'] ? date('d/m/Y', strtotime($pedido['data_entrega'])) : 'Não definida'; ?></div>
                            </div>
                            <?php if (!empty($pedido['mensagem'])): ?>
                                <div style="margin-top: 15px;"><strong><i class="fas fa-comment"></i> Mensagem:</strong><br><em><?php echo htmlspecialchars($pedido['mensagem']); ?></em></div>
                            <?php endif; ?>
                            <?php if ($pedido['status'] === 'Pendente'): ?>
                                <div class="action-buttons">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="pedido_id" value="<?php echo $pedido['id_pedido']; ?>">
                                        <input type="hidden" name="action" value="aceitar">
                                        <button type="submit" class="btn-action btn-aceitar"><i class="fas fa-check"></i> Aceitar</button>
                                    </form>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="pedido_id" value="<?php echo $pedido['id_pedido']; ?>">
                                        <input type="hidden" name="action" value="rejeitar">
                                        <button type="submit" class="btn-action btn-rejeitar" onclick="return confirm('Tem certeza que deseja rejeitar este pedido?')"><i class="fas fa-times"></i> Rejeitar</button>
                                    </form>
                                </div>
                            <?php elseif ($pedido['status'] === 'Aceite'): ?>
                                <div class="action-buttons">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="pedido_id" value="<?php echo $pedido['id_pedido']; ?>">
                                        <input type="hidden" name="action" value="concluir">
                                        <button type="submit" class="btn-action btn-concluir"><i class="fas fa-check-double"></i> Marcar como Concluído</button>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
