<?php
require_once 'db.php';
session_start();
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clientes - Cake Garden</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courgette&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Libre+Baskerville:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Lobster+Two:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/style.css">
    
    <style>
        .clientes-container {
            max-width: 1200px;
            margin: 40px auto;
            padding: 0 20px;
        }
        
        .clientes-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .clientes-header h1 {
            color: #93622B;
            font-family: 'Cormorant Garamond', serif;
            font-size: 42px;
            margin-bottom: 15px;
        }
        
        .clientes-header p {
            color: #666;
            font-size: 18px;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .depoimentos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .depoimento-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .depoimento-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .cliente-foto {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 15px;
            border: 3px solid #93622B;
        }
        
        .cliente-info h3 {
            color: #333;
            font-family: 'Lobster Two', cursive;
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .cliente-info .data {
            color: #999;
            font-size: 14px;
        }
        
        .avaliacao {
            color: #FFD700;
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .depoimento-texto {
            color: #666;
            line-height: 1.6;
            font-style: italic;
            position: relative;
        }
        
        .depoimento-texto::before {
            content: '"';
            font-size: 60px;
            color: #f0f0f0;
            position: absolute;
            top: -20px;
            left: -15px;
            font-family: serif;
            z-index: -1;
        }
        
        .compartilhar-depoimento {
            background-color: #FFF8F0;
            padding: 40px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 50px;
        }
        
        .compartilhar-depoimento h2 {
            color: #93622B;
            font-family: 'Lobster Two', cursive;
            font-size: 32px;
            margin-bottom: 20px;
        }
        
        .compartilhar-depoimento p {
            color: #666;
            font-size: 16px;
            max-width: 700px;
            margin: 0 auto 25px;
        }
        
        .btn-compartilhar {
            display: inline-block;
            background-color: #93622B;
            color: white;
            padding: 12px 25px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-compartilhar:hover {
            background-color: #7A5023;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .decorative-line {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .line {
            height: 2px;
            width: 100px;
            background-color: #93622B;
            margin: 0 15px;
        }
    </style>
</head>
<body>
    <!--navbar-->
    <header>
        <div class="barra">
            <div class="logo">
                <img src="../image/Cake_Garden__3_-removebg-preview.png" alt="Cake Garden Logo">
            </div>
            <!--barra de pesquisa-->
            <div class="search-container">
                <input type="search" class="search-input" placeholder="search">
                <button class="search-button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            <!--fim da barra de pesquisa-->
            <ul class="nav-list-icon">    
                <?php if(isset($_SESSION['user_id'])): ?>
                    <div class="user-initial">
                        <span><?php echo strtoupper(substr($_SESSION['user_name'], 0, 1)); ?></span>
                    </div>
                    <a href="logout.php" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> sair
                    </a>
                <?php else: ?>
                    <a href="./login.php">
                        <i class="fa-solid fa-user"></i>
                    </a>
                <?php endif; ?>
            </ul>
        </div>

        <!--menu-->
        <div class="menu-bar">
            <ul>
                <li><a href="index.php"><i class="fa-solid fa-house"></i>INÍCIO</a></li>
                <li><a href="about.php"><i class="fa-solid fa-handshake"></i>SOBRE</a></li>
                <li><a href="loja2.php"><i class="fa-solid fa-bag-shopping"></i>LOJA</a></li>
                <li style="width: 150px;"><a href="contacto.php"><i class="fa-solid fa-phone"></i>CONTACTO</a></li>
                <li><a href="blog.php"><i class="fa-solid fa-pen-to-square"></i>BLOG</a></li>
                <li><a href="galeria.php" class="active"><i class="fa-solid fa-images"></i>GALERIA</a></li>
                <li style="width: 150px;"><a href="cliente.php" class="active"><i class="fa-solid fa-star"></i>CLIENTE</a></li>
            </ul>
        </div>
    </header>
    <!-- fim do menu-->
    
    <div class="clientes-container">
        <div class="clientes-header">
            <div class="decorative-line">
                <div class="line"></div> 
                <h1>Depoimentos dos Nossos Clientes</h1>
                <div class="line"></div>
            </div>
            <p>Descubra o que nossos clientes têm a dizer sobre nossas delícias e serviços</p>
        </div>
        
        <!-- Seção de Depoimentos -->
        <div class="depoimentos-grid">
            <?php foreach ($depoimentos as $depoimento): ?>
            <div class="depoimento-card">
                <div class="depoimento-header">
                    <img src="<?php echo $depoimento['foto']; ?>" alt="<?php echo $depoimento['nome']; ?>" class="cliente-foto">
                    <div class="cliente-info">
                        <h3><?php echo $depoimento['nome']; ?></h3>
                        <span class="data"><?php echo date('d/m/Y', strtotime($depoimento['data'])); ?></span>
                    </div>
                </div>
                <div class="avaliacao">
                    <?php 
                    for ($i = 1; $i <= 5; $i++) {
                        if ($i <= $depoimento['avaliacao']) {
                            echo '<i class="fas fa-star"></i>';
                        } else {
                            echo '<i class="far fa-star"></i>';
                        }
                    }
                    ?>
                </div>
                <div class="depoimento-texto">
                    <p><?php echo $depoimento['comentario']; ?></p>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Seção para compartilhar depoimento -->
        <div class="compartilhar-depoimento">
            <h2>Compartilhe sua experiência</h2>
            <p>Adoraríamos ouvir sobre sua experiência com nossos bolos e serviços. Seu feedback é muito importante para continuarmos melhorando e oferecendo o melhor para você!</p>
            <a href="enviar-depoimento.php" class="btn-compartilhar">ENVIAR MEU DEPOIMENTO</a>
        </div>
    </div>
    
    <!--footer-->
    <footer>
        <div class="footer_content">
            <!-- Seção de contato -->
            <div class="footer_section footer_contactos">
                <h3><i class="fa-solid fa-phone"></i> CONTACTOS</h3>
                <ul>
                    <li><a href="#"><i class="fa-solid fa-mobile"></i> +351 912 345 678</a></li>
                    <li><a href="#"><i class="fa-solid fa-envelope"></i> <EMAIL></a></li>
                    <li><a href="#"><i class="fa-solid fa-location-dot"></i> Av. da Liberdade, 123, Lisboa</a></li>
                </ul>
            </div>

            <!-- Seção de entrega -->
            <div class="footer_section footer_entrega">
                <h3><i class="fa-solid fa-truck"></i> ENTREGA RÁPIDA</h3>
                
                 <ul>
                    <li><a href="prazo_entrega.php">Prazo de entrega e Áreas atendidas</a></li>
                </ul>
           
            </div>

            <!-- Seção de redes sociais -->
            <div class="footer_section footer_social">
                <h3><i class="fa-solid fa-globe"></i> SOCIAL</h3>
                <ul>
                    <li><a href="#"><i class="fa-brands fa-facebook"></i></a></li>
                    <li><a href="#"><i class="fa-brands fa-instagram"></i></a></li>
                    <li><a href="#"><i class="fa-brands fa-twitter"></i></a></li>
                </ul>
            </div>
        </div>

        <!-- Copyright -->
        <div class="footer_copyright">
            &copy; 2025 Cake Garden. Todos os direitos reservados.
        </div>
    </footer>
</body>
</html>

