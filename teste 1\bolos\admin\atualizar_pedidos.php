<?php
include '../db.php';

try {
    // Verificar se as colunas necessárias existem na tabela pedidos
    $stmt = $pdo->query("DESCRIBE pedidos");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>Atualizando estrutura da tabela 'pedidos'...</h2>";
    
    // Verificar e adicionar coluna 'data_entrega' se não existir
    if (!in_array('data_entrega', $columns)) {
        $pdo->exec("ALTER TABLE pedidos ADD COLUMN data_entrega DATE NULL");
        echo "<p style='color: green;'>✓ Coluna 'data_entrega' adicionada com sucesso!</p>";
    } else {
        echo "<p style='color: blue;'>✓ Coluna 'data_entrega' já existe.</p>";
    }
    
    // Verificar e adicionar coluna 'mensagem' se não existir
    if (!in_array('mensagem', $columns)) {
        $pdo->exec("ALTER TABLE pedidos ADD COLUMN mensagem TEXT NULL");
        echo "<p style='color: green;'>✓ Coluna 'mensagem' adicionada com sucesso!</p>";
    } else {
        echo "<p style='color: blue;'>✓ Coluna 'mensagem' já existe.</p>";
    }
    
    // Atualizar o status padrão para 'Pendente' se necessário
    $pdo->exec("ALTER TABLE pedidos MODIFY COLUMN status VARCHAR(50) NOT NULL DEFAULT 'Pendente'");
    echo "<p style='color: green;'>✓ Status padrão atualizado para 'Pendente'!</p>";
    
    echo "<h3 style='color: green;'>Estrutura da tabela pedidos atualizada com sucesso!</h3>";
    
    // Mostrar a estrutura atual da tabela
    echo "<h3>Estrutura atual da tabela 'pedidos':</h3>";
    $stmt = $pdo->query("DESCRIBE pedidos");
    $columns_info = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th></tr>";
    foreach ($columns_info as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>Erro: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3 {
    color: #93622B;
}

table {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

th, td {
    padding: 10px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background: #93622B;
    color: white;
}
</style>

<p><a href="dashboard.php" style="display: inline-block; padding: 10px 20px; background-color: #93622B; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px;">Voltar ao Dashboard</a></p>
<p><a href="pedidos.php" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-top: 10px;">Ver Pedidos</a></p>
