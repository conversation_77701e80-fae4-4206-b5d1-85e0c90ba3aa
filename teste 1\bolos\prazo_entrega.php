<?php
include 'db.php';
session_start();
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prazos de Entrega - Cake Garden</title>
    <link href="https://fonts.googleapis.com/css2?family=Courgette&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
       <!-- Font Awesome -->
     <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courgette&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Libre+Baskerville:wght@400;700&display=swap" rel="stylesheet">

     <!-- Google Fonts cake garden-->
     <link href="https://fonts.googleapis.com/css2?family=Playfair+Display&display=swap" rel="stylesheet">

     <!--H2 fonts-->
     <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lobster+Two:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
    <!-- CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <!--H2 fonts-->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lobster+Two:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .delivery-container {
            max-width: 900px;
            margin: 50px auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .delivery-title {
            font-family: 'Lobster Two', cursive;
            color: #93622B;
            font-size: 36px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .delivery-intro {
            font-size: 18px;
            color: #555;
            line-height: 1.6;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .delivery-section {
            margin-bottom: 40px;
        }
        
        .delivery-section h2 {
            color: #93622B;
            font-family: 'Playfair Display', serif;
            font-size: 24px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #BBA182;
        }
        
        .delivery-section p {
            font-size: 16px;
            color: #555;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        
        .delivery-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .delivery-table th, 
        .delivery-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .delivery-table th {
            background-color: #f5f0e8;
            color: #93622B;
            font-weight: bold;
        }
        
        .delivery-table tr:hover {
            background-color: #f9f5f0;
        }
        
        .important-note {
            background-color: #f9f5f0;
            border-left: 4px solid #93622B;
            padding: 15px;
            margin: 20px 0;
        }
        
        .important-note h3 {
            color: #93622B;
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .important-note p {
            margin: 0;
        }
        
        .delivery-icon {
            font-size: 24px;
            color: #93622B;
            margin-right: 10px;
            vertical-align: middle;
        }
        
        .areas-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .area-item {
            background-color: #f9f5f0;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .area-item i {
            display: block;
            font-size: 24px;
            color: #93622B;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!--navbar-->
    <header>
        <div class="barra">
            <div class="logo">
                <img src="../image/Cake_Garden__3_-removebg-preview.png" alt="Cake Garden Logo">
            </div>
            <!--barra de pesquisa-->
            <div class="search-container">
                <input type="search" class="search-input" placeholder="search">
                <button class="search-button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            <!--fim da barra de pesquisa-->
            <ul class="nav-list-icon">    
                <a href="./login.php">
                    <i class="fa-solid fa-user"></i>
                </a>
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> sair
                </a>
            </ul>
        </div>
        <!--menu-->
        <div class="menu-bar">
            <ul>
                <li><a href="index.php"><i class="fa-solid fa-house"></i>INÍCIO</a></li>
                <li><a href="about us.php"><i class="fa-solid fa-handshake"></i>SOBRE</a></li>
                <li><a href="loja2.php"><i class="fa-solid fa-bag-shopping"></i>LOJA</a></li>
                <li style="width: 150px;"><a href="contacto.php"><i class="fa-solid fa-phone"></i>CONTACTO</a></li>
                <li><a href="blog.php"><i class="fa-solid fa-pen-to-square"></i>BLOG</a></li>
                <li><a href="galeria.php"><i class="fa-solid fa-images"></i>GALERIA</a></li>
                <li style="width: 150px;"><a href="cliente.php"><i class="fa-solid fa-star"></i>CLIENTE</a></li>
            </ul>
        </div>
    </header>
    <!-- fim do menu-->
    
    <div class="delivery-container">
        <h1 class="delivery-title">Prazos de Entrega</h1>
        <p class="delivery-intro">
            Na Cake Garden, nos esforçamos para entregar os bolos mais frescos e deliciosos diretamente à sua porta.
            Conheça nossos prazos de entrega e áreas atendidas.
        </p>
        
        <div class="delivery-section">
            <h2><i class="fa-solid fa-calendar-days delivery-icon"></i>Prazos de Entrega</h2>
            <p>
                Para garantir a qualidade e personalização dos nossos produtos, trabalhamos com os seguintes prazos:
            </p>
            
            <table class="delivery-table">
                <thead>
                    <tr>
                        <th>Tipo de Bolo</th>
                        <th>Prazo Mínimo</th>
                        <th>Observações</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Bolos Simples</td>
                        <td>2 dias úteis</td>
                        <td>Bolos tradicionais sem decoração elaborada</td>
                    </tr>
                    <tr>
                        <td>Bolos Recheados</td>
                        <td>3 dias úteis</td>
                        <td>Bolos com recheios especiais</td>
                    </tr>
                    <tr>
                        <td>Bolos com Cobertura</td>
                        <td>3 dias úteis</td>
                        <td>Bolos com coberturas especiais como ganache ou chantilly</td>
                    </tr>
                    <tr>
                        <td>Bolos Personalizados</td>
                        <td>5 dias úteis</td>
                        <td>Bolos com decoração personalizada ou temática</td>
                    </tr>
                    <tr>
                        <td>Bolos para Eventos</td>
                        <td>7 dias úteis</td>
                        <td>Bolos para casamentos, aniversários especiais ou eventos corporativos</td>
                    </tr>
                </tbody>
            </table>
            
            <div class="important-note">
                <h3><i class="fa-solid fa-circle-exclamation"></i> Aviso Importante</h3>
                <p>
                    Todas as encomendas devem ser feitas com pelo menos 2 dias de antecedência para garantirmos 
                    a qualidade e personalização dos nossos produtos. Para eventos especiais como casamentos, 
                    recomendamos encomendar com pelo menos 2 semanas de antecedência.
                </p>
            </div>
        </div>
        
        <div class="delivery-section">
            <h2><i class="fa-solid fa-truck-fast delivery-icon"></i>Horários de Entrega</h2>
            <p>
                Nossas entregas são realizadas nos seguintes horários:
            </p>
            <ul>
                <li>Segunda a sexta-feira: 9h às 18h</li>
                <li>Sábados: 9h às 15h</li>
                <li>Domingos e feriados: Entregas especiais mediante consulta prévia</li>
            </ul>
            <p>
                No momento da finalização do pedido, você poderá escolher o melhor horário para receber o seu bolo 
                dentro das opções disponíveis.
            </p>
        </div>
        
        <div class="delivery-section">
            <h2><i class="fa-solid fa-map-location-dot delivery-icon"></i>Áreas Atendidas</h2>
            <p>
                Atualmente, realizamos entregas nas seguintes áreas de Lisboa:
            </p>
            
            <div class="areas-list">
                <div class="area-item">
                    <i class="fa-solid fa-location-dot"></i>
                    <span>Centro de Lisboa</span>
                </div>
                <div class="area-item">
                    <i class="fa-solid fa-location-dot"></i>
                    <span>Belém</span>
                </div>
                <div class="area-item">
                    <i class="fa-solid fa-location-dot"></i>
                    <span>Alfama</span>
                </div>
                <div class="area-item">
                    <i class="fa-solid fa-location-dot"></i>
                    <span>Baixa-Chiado</span>
                </div>
                <div class="area-item">
                    <i class="fa-solid fa-location-dot"></i>
                    <span>Parque das Nações</span>
                </div>
                <div class="area-item">
                    <i class="fa-solid fa-location-dot"></i>
                    <span>Alcântara</span>
                </div>
                <div class="area-item">
                    <i class="fa-solid fa-location-dot"></i>
                    <span>Campo de Ourique</span>
                </div>
                <div class="area-item">
                    <i class="fa-solid fa-location-dot"></i>
                    <span>Avenidas Novas</span>
                </div>
            </div>
            
            <p>
                Para outras localidades, entre em contato conosco para verificar a disponibilidade e possíveis 
                taxas adicionais de entrega.
            </p>
        </div>
        
        <div class="delivery-section">
            <h2><i class="fa-solid fa-circle-info delivery-icon"></i>Informações Adicionais</h2>
            <p>
                <strong>Taxa de Entrega:</strong> A taxa de entrega é calculada com base na distância e será informada 
                durante o processo de checkout.
            </p>
            <p>
                <strong>Retirada na Loja:</strong> Você também pode optar por retirar seu pedido diretamente em nossa loja, 
                sem custo adicional. Nossa loja está localizada na Av. da Liberdade, 123, Lisboa.
            </p>
            <p>
                <strong>Entregas Especiais:</strong> Para entregas urgentes ou fora do horário comercial, entre em contato 
                conosco para verificar a disponibilidade e custos adicionais.
            </p>
        </div>
    </div>
    
    <!--footer-->
    <footer>
        <div class="footer_content">
            <!-- Seção de contato -->
            <div class="footer_section footer_contactos">
                <h3><i class="fa-solid fa-phone"></i> CONTACTOS</h3>
                <ul>
                    <li><a href="#"><i class="fa-solid fa-mobile"></i> +351 912 345 678</a></li>
                    <li><a href="#"><i class="fa-solid fa-envelope"></i> <EMAIL></a></li>
                    <li><a href="#"><i class="fa-solid fa-location-dot"></i> Av. da Liberdade, 123, Lisboa</a></li>
                </ul>
            </div>

            <!-- Seção de entrega -->
            <div class="footer_section footer_entrega">
                <h3><i class="fa-solid fa-truck"></i> ENTREGA RÁPIDA</h3>
                <ul>
                    <li><a href="prazo_entrega.php">Prazo de entrega e Áreas atendidas</a></li>
                </ul>   
            </div>

            <!-- Seção de redes sociais -->
            <div class="footer_section footer_social">
                <h3><i class="fa-solid fa-globe"></i> SOCIAL</h3>
                <ul>
                    <li><a href="#"><i class="fa-brands fa-facebook"></i></a></li>
                    <li><a href="#"><i class="fa-brands fa-instagram"></i></a></li>
                    <li><a href="#"><i class="fa-brands fa-twitter"></i></a></li>
                </ul>
            </div>
        </div>

        <!-- Copyright -->
        <div class="footer_copyright">
            &copy; 2025 Cake Garden. Todos os direitos reservados.
        </div>
    </footer>
</body>
</html>