<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuração do Sistema - Cake Garden</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body {
            background: #f5f5f5;
            font-family: 'Arial', sans-serif;
            padding: 20px;
        }
        
        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 30px;
            color: #93622B;
        }
        
        .step {
            background: #f8f9fa;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            border-left: 4px solid #93622B;
        }
        
        .step h3 {
            color: #93622B;
            margin-bottom: 10px;
        }
        
        .step p {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .credentials {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #28a745;
        }
        
        .credentials strong {
            color: #155724;
        }
        
        .btn-setup {
            background: #93622B;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        
        .btn-setup:hover {
            background: #7A5023;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1><i class="fas fa-cog"></i> Configuração do Sistema Cake Garden</h1>
            <p>Siga os passos abaixo para configurar o painel administrativo</p>
        </div>
        
        <div class="step">
            <h3><i class="fas fa-database"></i> Passo 1: Configurar Base de Dados</h3>
            <p>Primeiro, você precisa configurar a tabela de utilizadores para suportar diferentes tipos de usuários (admin, confeiteiro, cliente).</p>
            <a href="verificar_tabela.php" class="btn-setup">
                <i class="fas fa-play"></i> Executar Configuração da BD
            </a>
        </div>
        
        <div class="step">
            <h3><i class="fas fa-user-shield"></i> Passo 2: Credenciais do Administrador</h3>
            <p>Após executar a configuração da base de dados, será criado um usuário administrador padrão:</p>
            <div class="credentials">
                <strong>Email:</strong> <EMAIL><br>
                <strong>Senha:</strong> admin123
            </div>
            <div class="warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Importante:</strong> Altere estas credenciais após o primeiro login por motivos de segurança!
            </div>
        </div>
        
        <div class="step">
            <h3><i class="fas fa-sign-in-alt"></i> Passo 3: Fazer Login</h3>
            <p>Use as credenciais acima para fazer login no sistema. Você será automaticamente redirecionado para o painel administrativo.</p>
            <a href="../bolos/login.php" class="btn-setup">
                <i class="fas fa-sign-in-alt"></i> Ir para Login
            </a>
        </div>
        
        <div class="step">
            <h3><i class="fas fa-users-cog"></i> Passo 4: Gerir Confeiteiros</h3>
            <p>No painel administrativo, você poderá:</p>
            <ul>
                <li>Adicionar novos confeiteiros</li>
                <li>Visualizar lista de confeiteiros cadastrados</li>
                <li>Remover confeiteiros quando necessário</li>
                <li>Gerir todos os aspectos do sistema</li>
            </ul>
        </div>
        
        <div class="step">
            <h3><i class="fas fa-shield-alt"></i> Funcionalidades de Segurança</h3>
            <p>O sistema inclui:</p>
            <ul>
                <li><strong>Controle de Acesso:</strong> Apenas administradores podem acessar o painel</li>
                <li><strong>Sessões Seguras:</strong> Sistema de autenticação baseado em sessões</li>
                <li><strong>Senhas Criptografadas:</strong> Todas as senhas são armazenadas com hash seguro</li>
                <li><strong>Validação de Dados:</strong> Proteção contra injeção SQL e XSS</li>
            </ul>
        </div>
        
        <div class="success">
            <h3><i class="fas fa-check-circle"></i> Sistema Pronto!</h3>
            <p>Após completar os passos acima, o sistema estará totalmente configurado e pronto para uso.</p>
            <p>O ícone de configuração <i class="fas fa-cog"></i> aparecerá no header apenas para usuários administradores logados.</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="../index.php" class="btn-setup">
                <i class="fas fa-home"></i> Voltar ao Site
            </a>
            <a href="dashboard.php" class="btn-setup">
                <i class="fas fa-tachometer-alt"></i> Ir para Dashboard
            </a>
        </div>
    </div>
</body>
</html>
