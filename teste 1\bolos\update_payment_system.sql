-- Script para atualizar a base de dados com sistema de pagamentos
-- Execute este script para adicionar os campos necessários para o sistema de pagamento

USE loja_bolos;

-- Adicionar campos à tabela pedidos para suporte a pagamentos
ALTER TABLE pedidos 
ADD COLUMN preco_total DECIMAL(10,2) DEFAULT 0.00 AFTER status,
ADD COLUMN status_pagamento ENUM('Pendente', 'Processando', 'Pago', 'Falhado', 'Reembolsado') DEFAULT 'Pendente' AFTER preco_total,
ADD COLUMN metodo_pagamento VARCHAR(50) NULL AFTER status_pagamento,
ADD COLUMN referencia_pagamento VARCHAR(255) NULL AFTER metodo_pagamento,
ADD COLUMN data_pagamento DATETIME NULL AFTER referencia_pagamento;

-- Criar tabela para histórico de pagamentos
CREATE TABLE IF NOT EXISTS pagamentos (
    id_pagamento INT AUTO_INCREMENT PRIMARY KEY,
    id_pedido INT NOT NULL,
    valor DECIMAL(10,2) NOT NULL,
    metodo_pagamento VARCHAR(50) NOT NULL,
    status_pagamento ENUM('Pendente', 'Processando', 'Pago', 'Falhado', 'Reembolsado') DEFAULT 'Pendente',
    referencia_externa VARCHAR(255) NULL,
    dados_pagamento JSON NULL,
    data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_pedido) REFERENCES pedidos(id_pedido) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Criar tabela para configurações de pagamento
CREATE TABLE IF NOT EXISTS configuracoes_pagamento (
    id_config INT AUTO_INCREMENT PRIMARY KEY,
    metodo VARCHAR(50) NOT NULL UNIQUE,
    ativo BOOLEAN DEFAULT FALSE,
    configuracoes JSON NOT NULL,
    data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Inserir configurações padrão para Stripe e PayPal
INSERT INTO configuracoes_pagamento (metodo, ativo, configuracoes) VALUES
('stripe', FALSE, '{"public_key": "", "secret_key": "", "webhook_secret": ""}'),
('paypal', FALSE, '{"client_id": "", "client_secret": "", "mode": "sandbox"}')
ON DUPLICATE KEY UPDATE configuracoes = VALUES(configuracoes);

-- Atualizar pedidos existentes com preço total calculado (se existirem)
UPDATE pedidos p
JOIN bolo b ON p.id_bolo = b.id_bolo
JOIN tipo_bolo tb ON b.id_tipo = tb.id_tipo
LEFT JOIN pesos pe ON b.id_peso = pe.id_peso
LEFT JOIN decoracao d ON b.id_decoracao = d.id_decoracao
SET p.preco_total = COALESCE(tb.preco, 0) + COALESCE(pe.preco, 0) + COALESCE(d.preco, 0)
WHERE p.preco_total = 0.00 OR p.preco_total IS NULL;

-- Criar índices para melhor performance
CREATE INDEX idx_pedidos_status_pagamento ON pedidos(status_pagamento);
CREATE INDEX idx_pagamentos_status ON pagamentos(status_pagamento);
CREATE INDEX idx_pagamentos_referencia ON pagamentos(referencia_externa);

COMMIT;
