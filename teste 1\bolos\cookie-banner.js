// <PERSON>ie Banner Management for Cake Garden
// Este script gere o banner de consentimento de cookies

document.addEventListener('DOMContentLoaded', function() {
    // Verificar se o utilizador já deu consentimento
    if (!getCookie('cookieConsent')) {
        showCookieBanner();
    }
});

function showCookieBanner() {
    // Criar o banner de cookies
    const banner = document.createElement('div');
    banner.id = 'cookie-banner';
    banner.innerHTML = `
        <div class="cookie-banner-content">
            <div class="cookie-banner-text">
                <h4><i class="fas fa-cookie-bite"></i> Utilizamos Cookies</h4>
                <p>Este site utiliza cookies para melhorar a sua experiência de navegação, analisar o tráfego e personalizar conteúdo. 
                Ao continuar a navegar, concorda com a nossa utilização de cookies.</p>
            </div>
            <div class="cookie-banner-buttons">
                <button onclick="acceptAllCookies()" class="btn-accept-all">
                    <i class="fas fa-check"></i> Aceitar Todos
                </button>
                <button onclick="acceptEssentialOnly()" class="btn-essential">
                    <i class="fas fa-cog"></i> Apenas Essenciais
                </button>
                <a href="politica_cookies.php" class="btn-more-info">
                    <i class="fas fa-info-circle"></i> Mais Informações
                </a>
            </div>
        </div>
    `;

    // Adicionar estilos CSS
    const style = document.createElement('style');
    style.textContent = `
        #cookie-banner {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #93622B, #7a5023);
            color: white;
            padding: 20px;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.3);
            z-index: 10000;
            font-family: 'Roboto', sans-serif;
            animation: slideUp 0.5s ease-out;
        }

        @keyframes slideUp {
            from {
                transform: translateY(100%);
            }
            to {
                transform: translateY(0);
            }
        }

        .cookie-banner-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 20px;
        }

        .cookie-banner-text {
            flex: 1;
            min-width: 300px;
        }

        .cookie-banner-text h4 {
            margin: 0 0 10px 0;
            font-size: 18px;
            font-weight: bold;
            color: #fff;
        }

        .cookie-banner-text p {
            margin: 0;
            font-size: 14px;
            line-height: 1.5;
            opacity: 0.9;
        }

        .cookie-banner-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }

        .cookie-banner-buttons button,
        .cookie-banner-buttons a {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-accept-all {
            background: #28a745;
            color: white;
        }

        .btn-accept-all:hover {
            background: #218838;
            transform: translateY(-2px);
        }

        .btn-essential {
            background: #6c757d;
            color: white;
        }

        .btn-essential:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .btn-more-info {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-more-info:hover {
            background: white;
            color: #93622B;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .cookie-banner-content {
                flex-direction: column;
                text-align: center;
            }

            .cookie-banner-buttons {
                justify-content: center;
                width: 100%;
            }

            .cookie-banner-buttons button,
            .cookie-banner-buttons a {
                flex: 1;
                min-width: 120px;
            }
        }

        @media (max-width: 480px) {
            .cookie-banner-buttons {
                flex-direction: column;
            }

            .cookie-banner-buttons button,
            .cookie-banner-buttons a {
                width: 100%;
            }
        }
    `;

    // Adicionar o estilo e o banner ao documento
    document.head.appendChild(style);
    document.body.appendChild(banner);
}

function acceptAllCookies() {
    // Definir cookie de consentimento para todos os cookies
    setCookie('cookieConsent', 'all', 365);
    setCookie('analyticsConsent', 'true', 365);
    setCookie('functionalConsent', 'true', 365);
    
    // Remover o banner
    hideCookieBanner();
    
    // Carregar scripts de análise se necessário
    loadAnalyticsScripts();
    
    // Mostrar mensagem de confirmação
    showConsentMessage('Obrigado! Todos os cookies foram aceites.');
}

function acceptEssentialOnly() {
    // Definir cookie de consentimento apenas para cookies essenciais
    setCookie('cookieConsent', 'essential', 365);
    setCookie('analyticsConsent', 'false', 365);
    setCookie('functionalConsent', 'false', 365);
    
    // Remover o banner
    hideCookieBanner();
    
    // Mostrar mensagem de confirmação
    showConsentMessage('Apenas cookies essenciais foram aceites.');
}

function hideCookieBanner() {
    const banner = document.getElementById('cookie-banner');
    if (banner) {
        banner.style.animation = 'slideDown 0.5s ease-out';
        setTimeout(() => {
            banner.remove();
        }, 500);
    }
}

function showConsentMessage(message) {
    // Criar mensagem de confirmação
    const messageDiv = document.createElement('div');
    messageDiv.innerHTML = `
        <div style="
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            z-index: 10001;
            font-family: 'Roboto', sans-serif;
            animation: fadeInOut 3s ease-in-out;
        ">
            <i class="fas fa-check-circle"></i> ${message}
        </div>
    `;

    // Adicionar animação CSS
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeInOut {
            0% { opacity: 0; transform: translateX(100%); }
            20% { opacity: 1; transform: translateX(0); }
            80% { opacity: 1; transform: translateX(0); }
            100% { opacity: 0; transform: translateX(100%); }
        }
        @keyframes slideDown {
            from { transform: translateY(0); }
            to { transform: translateY(100%); }
        }
    `;

    document.head.appendChild(style);
    document.body.appendChild(messageDiv);

    // Remover a mensagem após 3 segundos
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

function loadAnalyticsScripts() {
    // Carregar Google Analytics se o consentimento foi dado
    if (getCookie('analyticsConsent') === 'true') {
        // Exemplo de carregamento do Google Analytics
        // Substitua 'GA_MEASUREMENT_ID' pelo seu ID real
        /*
        const script = document.createElement('script');
        script.async = true;
        script.src = 'https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID';
        document.head.appendChild(script);

        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
        */
    }
}

// Funções auxiliares para gestão de cookies
function setCookie(name, value, days) {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
}

function getCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
}

function deleteCookie(name) {
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
}

// Função para revogar consentimento (pode ser chamada de uma página de configurações)
function revokeCookieConsent() {
    deleteCookie('cookieConsent');
    deleteCookie('analyticsConsent');
    deleteCookie('functionalConsent');
    
    // Recarregar a página para mostrar o banner novamente
    location.reload();
}

// Função para verificar o estado do consentimento
function getConsentStatus() {
    return {
        hasConsent: !!getCookie('cookieConsent'),
        consentType: getCookie('cookieConsent'),
        analytics: getCookie('analyticsConsent') === 'true',
        functional: getCookie('functionalConsent') === 'true'
    };
}

// Exportar funções para uso global
window.CookieManager = {
    acceptAll: acceptAllCookies,
    acceptEssential: acceptEssentialOnly,
    revoke: revokeCookieConsent,
    getStatus: getConsentStatus,
    show: showCookieBanner
};
