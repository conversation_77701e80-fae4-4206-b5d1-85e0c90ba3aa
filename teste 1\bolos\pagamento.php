<?php
session_start();
include 'db.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// Verificar se foi passado um ID de pedido
if (!isset($_GET['pedido_id'])) {
    header("Location: index.php");
    exit;
}

$pedido_id = $_GET['pedido_id'];
$user_id = $_SESSION['user_id'];

// Buscar informações do pedido
try {
    $stmt = $pdo->prepare("
        SELECT p.*, u.nome as cliente_nome, u.email as cliente_email,
               tb.nome_tipo, tb.preco as tipo_preco,
               pe.peso, pe.preco as peso_preco,
               d.nome_decoracao, d.preco as decoracao_preco
        FROM pedidos p
        JOIN utilizadores u ON p.id_utilizador = u.id_utilizador
        JOIN bolo b ON p.id_bolo = b.id_bolo
        JOIN tipo_bolo tb ON b.id_tipo = tb.id_tipo
        LEFT JOIN pesos pe ON b.id_peso = pe.id_peso
        LEFT JOIN decoracao d ON b.id_decoracao = d.id_decoracao
        WHERE p.id_pedido = ? AND p.id_utilizador = ?
    ");
    $stmt->execute([$pedido_id, $user_id]);
    $pedido = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$pedido) {
        header("Location: index.php");
        exit;
    }
    
    // Verificar se o pedido já foi pago
    if ($pedido['status_pagamento'] === 'Pago') {
        header("Location: pedido_sucesso.php?pedido_id=" . $pedido_id);
        exit;
    }
    
} catch (PDOException $e) {
    die("Erro ao buscar pedido: " . $e->getMessage());
}

// Buscar configurações de pagamento ativas
$stmt = $pdo->query("SELECT metodo, ativo, configuracoes FROM configuracoes_pagamento WHERE ativo = TRUE");
$metodos_pagamento = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagamento - Cake Garden</title>
    <link rel="stylesheet" href="../css/estilo.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .payment-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .order-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .order-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        .order-item:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 1.2em;
            color: #93622B;
        }
        .payment-methods {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }
        .payment-method {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .payment-method:hover, .payment-method.selected {
            border-color: #93622B;
            background: #f8f5f0;
        }
        .payment-method input[type="radio"] {
            margin: 0;
        }
        .payment-method-info {
            flex: 1;
        }
        .payment-method-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .payment-method-desc {
            color: #666;
            font-size: 0.9em;
        }
        .payment-form {
            display: none;
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .payment-form.active {
            display: block;
        }
        .btn-pay {
            background: #93622B;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s;
        }
        .btn-pay:hover {
            background: #7a4f23;
        }
        .btn-pay:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .security-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: center;
        }
        .security-info i {
            color: #28a745;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <h1><i class="fas fa-credit-card"></i> Finalizar Pagamento</h1>
        
        <!-- Resumo do Pedido -->
        <div class="order-summary">
            <h2>Resumo do Pedido #<?php echo $pedido['id_pedido']; ?></h2>
            <div class="order-item">
                <span>Tipo de Bolo: <?php echo htmlspecialchars($pedido['nome_tipo']); ?></span>
                <span>€<?php echo number_format($pedido['tipo_preco'], 2, ',', '.'); ?></span>
            </div>
            <?php if ($pedido['peso']): ?>
            <div class="order-item">
                <span>Peso: <?php echo htmlspecialchars($pedido['peso']); ?></span>
                <span>€<?php echo number_format($pedido['peso_preco'], 2, ',', '.'); ?></span>
            </div>
            <?php endif; ?>
            <?php if ($pedido['nome_decoracao']): ?>
            <div class="order-item">
                <span>Decoração: <?php echo htmlspecialchars($pedido['nome_decoracao']); ?></span>
                <span>€<?php echo number_format($pedido['decoracao_preco'], 2, ',', '.'); ?></span>
            </div>
            <?php endif; ?>
            <div class="order-item">
                <span>Total a Pagar</span>
                <span>€<?php echo number_format($pedido['preco_total'], 2, ',', '.'); ?></span>
            </div>
        </div>

        <!-- Métodos de Pagamento -->
        <h2>Escolha o Método de Pagamento</h2>
        <form id="payment-form" method="POST" action="processar_pagamento.php">
            <input type="hidden" name="pedido_id" value="<?php echo $pedido_id; ?>">
            
            <div class="payment-methods">
                <?php foreach ($metodos_pagamento as $metodo): ?>
                    <?php 
                    $config = json_decode($metodo['configuracoes'], true);
                    $metodo_nome = ucfirst($metodo['metodo']);
                    ?>
                    <div class="payment-method" onclick="selectPaymentMethod('<?php echo $metodo['metodo']; ?>')">
                        <input type="radio" name="metodo_pagamento" value="<?php echo $metodo['metodo']; ?>" id="<?php echo $metodo['metodo']; ?>">
                        <div class="payment-method-info">
                            <div class="payment-method-title">
                                <i class="fab fa-<?php echo $metodo['metodo']; ?>"></i>
                                <?php echo $metodo_nome; ?>
                            </div>
                            <div class="payment-method-desc">
                                <?php if ($metodo['metodo'] === 'stripe'): ?>
                                    Pagamento seguro com cartão de crédito/débito
                                <?php elseif ($metodo['metodo'] === 'paypal'): ?>
                                    Pague com sua conta PayPal ou cartão
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Formulários específicos para cada método -->
            <div id="stripe-form" class="payment-form">
                <h3>Pagamento com Cartão</h3>
                <div id="stripe-elements">
                    <!-- Stripe Elements será inserido aqui -->
                </div>
            </div>

            <div id="paypal-form" class="payment-form">
                <h3>Pagamento com PayPal</h3>
                <div id="paypal-button-container">
                    <!-- Botão PayPal será inserido aqui -->
                </div>
            </div>

            <button type="submit" class="btn-pay" id="pay-button" disabled>
                <i class="fas fa-lock"></i> Pagar €<?php echo number_format($pedido['preco_total'], 2, ',', '.'); ?>
            </button>
        </form>

        <div class="security-info">
            <i class="fas fa-shield-alt"></i>
            Seus dados de pagamento são protegidos com criptografia SSL de 256 bits
        </div>
    </div>

    <script>
        function selectPaymentMethod(method) {
            // Marcar o método selecionado
            document.getElementById(method).checked = true;
            
            // Remover seleção visual anterior
            document.querySelectorAll('.payment-method').forEach(el => el.classList.remove('selected'));
            
            // Adicionar seleção visual
            event.currentTarget.classList.add('selected');
            
            // Esconder todos os formulários
            document.querySelectorAll('.payment-form').forEach(form => form.classList.remove('active'));
            
            // Mostrar formulário específico
            document.getElementById(method + '-form').classList.add('active');
            
            // Habilitar botão de pagamento
            document.getElementById('pay-button').disabled = false;
        }
    </script>

    <!-- Scripts dos gateways de pagamento serão carregados dinamicamente -->
    <?php foreach ($metodos_pagamento as $metodo): ?>
        <?php if ($metodo['metodo'] === 'stripe'): ?>
            <script src="https://js.stripe.com/v3/"></script>
        <?php elseif ($metodo['metodo'] === 'paypal'): ?>
            <script src="https://www.paypal.com/sdk/js?client-id=<?php echo json_decode($metodo['configuracoes'], true)['client_id']; ?>&currency=EUR"></script>
        <?php endif; ?>
    <?php endforeach; ?>
</body>
</html>
