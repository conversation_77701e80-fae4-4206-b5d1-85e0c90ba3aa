<?php
include '../db.php';
session_start();

// Simular login de admin para demonstração
if (isset($_GET['demo']) && $_GET['demo'] === 'admin') {
    $_SESSION['user_id'] = 999;
    $_SESSION['user_name'] = 'Admin Demo';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['user_role'] = 'admin';
    $demo_message = "Sessão de administrador simulada ativada! Agora você pode ver o botão DASHBOARD na navbar.";
}

if (isset($_GET['demo']) && $_GET['demo'] === 'cliente') {
    $_SESSION['user_id'] = 998;
    $_SESSION['user_name'] = 'Cliente Demo';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['user_role'] = 'cliente';
    $demo_message = "Sessão de cliente simulada ativada! O botão DASHBOARD não aparece para clientes.";
}

if (isset($_GET['demo']) && $_GET['demo'] === 'logout') {
    session_destroy();
    session_start();
    $demo_message = "Sessão encerrada! Agora você está como visitante.";
}

require_once '../includes/header.php';
?>

<style>
    .demo-container {
        max-width: 800px;
        margin: 50px auto;
        padding: 30px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .demo-header {
        text-align: center;
        margin-bottom: 30px;
        color: #93622B;
    }
    
    .demo-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin: 20px 0;
        flex-wrap: wrap;
    }
    
    .demo-btn {
        background: #93622B;
        color: white;
        padding: 12px 20px;
        border: none;
        border-radius: 5px;
        text-decoration: none;
        transition: background 0.3s;
        display: inline-block;
    }
    
    .demo-btn:hover {
        background: #7A5023;
    }
    
    .demo-btn.cliente {
        background: #007bff;
    }
    
    .demo-btn.cliente:hover {
        background: #0056b3;
    }
    
    .demo-btn.logout {
        background: #dc3545;
    }
    
    .demo-btn.logout:hover {
        background: #c82333;
    }
    
    .status-box {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
        border-left: 4px solid #93622B;
    }
    
    .message {
        background: #d4edda;
        color: #155724;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
        border: 1px solid #c3e6cb;
    }
    
    .instruction {
        background: #e2e3e5;
        padding: 15px;
        border-radius: 5px;
        margin: 20px 0;
        border-left: 4px solid #6c757d;
    }
    
    .highlight {
        background: #fff3cd;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #ffeaa7;
        margin: 10px 0;
    }
</style>

<div class="demo-container">
    <div class="demo-header">
        <h1><i class="fas fa-flask"></i> Demonstração do Botão Dashboard</h1>
        <p>Esta página permite testar a funcionalidade do botão Dashboard na navbar</p>
    </div>
    
    <?php if (isset($demo_message)): ?>
        <div class="message">
            <i class="fas fa-info-circle"></i> <?php echo $demo_message; ?>
        </div>
    <?php endif; ?>
    
    <div class="status-box">
        <h3><i class="fas fa-user"></i> Status Atual da Sessão:</h3>
        <?php if (isset($_SESSION['user_id'])): ?>
            <p><strong>Usuário:</strong> <?php echo htmlspecialchars($_SESSION['user_name']); ?></p>
            <p><strong>Email:</strong> <?php echo htmlspecialchars($_SESSION['user_email']); ?></p>
            <p><strong>Tipo:</strong> <?php echo htmlspecialchars($_SESSION['user_role']); ?></p>
            <p><strong>ID:</strong> <?php echo htmlspecialchars($_SESSION['user_id']); ?></p>
        <?php else: ?>
            <p><strong>Status:</strong> Não logado (visitante)</p>
        <?php endif; ?>
    </div>
    
    <div class="instruction">
        <h3><i class="fas fa-eye"></i> Como Testar:</h3>
        <ol>
            <li><strong>Olhe para a navbar acima</strong> - Verifique se o botão DASHBOARD aparece ou não</li>
            <li><strong>Use os botões abaixo</strong> para simular diferentes tipos de usuário</li>
            <li><strong>Observe as mudanças</strong> na navbar após cada clique</li>
        </ol>
    </div>
    
    <div class="demo-buttons">
        <a href="?demo=admin" class="demo-btn">
            <i class="fas fa-user-shield"></i> Simular Admin
        </a>
        <a href="?demo=cliente" class="demo-btn cliente">
            <i class="fas fa-user"></i> Simular Cliente
        </a>
        <a href="?demo=logout" class="demo-btn logout">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>
    
    <div class="highlight">
        <h4><i class="fas fa-lightbulb"></i> Comportamento Esperado:</h4>
        <ul>
            <li><strong>Admin:</strong> Botão DASHBOARD visível na navbar (com estilo especial)</li>
            <li><strong>Cliente:</strong> Botão DASHBOARD não aparece</li>
            <li><strong>Visitante:</strong> Botão DASHBOARD não aparece</li>
        </ul>
    </div>
    
    <div class="instruction">
        <h3><i class="fas fa-cog"></i> Funcionalidades do Botão Dashboard:</h3>
        <ul>
            <li>Aparece apenas para usuários com role 'admin'</li>
            <li>Posicionado após o botão "CLIENTE" na navbar</li>
            <li>Estilo visual diferenciado (gradiente e sombra)</li>
            <li>Link direto para o painel administrativo</li>
            <li>Ícone de dashboard (tachometer-alt)</li>
            <li>Estado ativo quando estiver nas páginas admin</li>
        </ul>
    </div>
    
    <div style="text-align: center; margin-top: 30px;">
        <a href="../index.php" class="demo-btn">
            <i class="fas fa-home"></i> Voltar ao Site
        </a>
        <?php if (isset($_SESSION['user_id']) && $_SESSION['user_role'] === 'admin'): ?>
        <a href="dashboard.php" class="demo-btn">
            <i class="fas fa-tachometer-alt"></i> Ir para Dashboard
        </a>
        <?php endif; ?>
    </div>
</div>

<?php
require_once '../includes/footer.php';
?>
