<?php
/**
 * Script de teste do sistema de pagamento
 * Use este script para verificar se tudo está funcionando corretamente
 */

include 'db.php';

echo "<h1>Teste do Sistema de Pagamento - Cake Garden</h1>";

// Função para exibir resultado do teste
function exibirResultado($teste, $sucesso, $detalhes = '') {
    $icon = $sucesso ? "✅" : "❌";
    $cor = $sucesso ? "green" : "red";
    echo "<div style='margin: 10px 0; padding: 10px; border-left: 4px solid $cor;'>";
    echo "<strong>$icon $teste</strong>";
    if ($detalhes) {
        echo "<br><small style='color: #666;'>$detalhes</small>";
    }
    echo "</div>";
}

try {
    echo "<h2>1. Verificação da Base de Dados</h2>";
    
    // Testar conexão com a base de dados
    exibirResultado("Conexão com a base de dados", true, "Conectado com sucesso");
    
    // Verificar se as tabelas existem
    $tabelas_necessarias = ['pedidos', 'pagamentos', 'configuracoes_pagamento'];
    foreach ($tabelas_necessarias as $tabela) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$tabela'");
        $existe = $stmt->rowCount() > 0;
        exibirResultado("Tabela '$tabela'", $existe, $existe ? "Tabela encontrada" : "Tabela não encontrada");
    }
    
    // Verificar colunas da tabela pedidos
    $colunas_necessarias = ['preco_total', 'status_pagamento', 'metodo_pagamento'];
    foreach ($colunas_necessarias as $coluna) {
        $stmt = $pdo->query("SHOW COLUMNS FROM pedidos LIKE '$coluna'");
        $existe = $stmt->rowCount() > 0;
        exibirResultado("Coluna 'pedidos.$coluna'", $existe, $existe ? "Coluna encontrada" : "Coluna não encontrada");
    }
    
    echo "<h2>2. Verificação de Configurações</h2>";
    
    // Verificar configurações de pagamento
    $stmt = $pdo->query("SELECT metodo, ativo, configuracoes FROM configuracoes_pagamento");
    $configuracoes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($configuracoes)) {
        exibirResultado("Configurações de pagamento", false, "Nenhuma configuração encontrada");
    } else {
        foreach ($configuracoes as $config) {
            $config_data = json_decode($config['configuracoes'], true);
            $metodo = ucfirst($config['metodo']);
            $ativo = $config['ativo'] ? "Ativo" : "Inativo";
            
            if ($config['metodo'] === 'stripe') {
                $configurado = !empty($config_data['public_key']) && !empty($config_data['secret_key']);
                exibirResultado("$metodo ($ativo)", $configurado, 
                    $configurado ? "Chaves configuradas" : "Chaves não configuradas");
            } elseif ($config['metodo'] === 'paypal') {
                $configurado = !empty($config_data['client_id']) && !empty($config_data['client_secret']);
                exibirResultado("$metodo ($ativo)", $configurado, 
                    $configurado ? "Credenciais configuradas" : "Credenciais não configuradas");
            }
        }
    }
    
    echo "<h2>3. Verificação de Ficheiros</h2>";
    
    // Verificar se os ficheiros necessários existem
    $ficheiros_necessarios = [
        'pagamento.php' => 'Página de pagamento',
        'processar_pagamento.php' => 'Processador de pagamentos',
        'webhook_stripe.php' => 'Webhook do Stripe',
        'admin/configuracoes_pagamento.php' => 'Painel de configuração',
        'composer.json' => 'Configuração do Composer'
    ];
    
    foreach ($ficheiros_necessarios as $ficheiro => $descricao) {
        $existe = file_exists($ficheiro);
        exibirResultado($descricao, $existe, $existe ? "Ficheiro encontrado" : "Ficheiro não encontrado");
    }
    
    echo "<h2>4. Verificação de Dependências</h2>";
    
    // Verificar se o autoload do Composer existe
    $composer_instalado = file_exists('vendor/autoload.php');
    exibirResultado("Dependências do Composer", $composer_instalado, 
        $composer_instalado ? "Dependências instaladas" : "Execute 'composer install'");
    
    if ($composer_instalado) {
        require_once 'vendor/autoload.php';
        
        // Verificar se as classes do Stripe estão disponíveis
        $stripe_disponivel = class_exists('\Stripe\Stripe');
        exibirResultado("SDK do Stripe", $stripe_disponivel, 
            $stripe_disponivel ? "SDK carregado" : "SDK não encontrado");
    }
    
    echo "<h2>5. Teste de Funcionalidades</h2>";
    
    // Testar cálculo de preços
    try {
        $stmt = $pdo->query("SELECT id_tipo, preco FROM tipo_bolo LIMIT 1");
        $tipo_bolo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $pdo->query("SELECT id_peso, preco FROM pesos LIMIT 1");
        $peso = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($tipo_bolo && $peso) {
            $preco_total = $tipo_bolo['preco'] + $peso['preco'];
            exibirResultado("Cálculo de preços", true, 
                "Exemplo: €" . number_format($preco_total, 2, ',', '.'));
        } else {
            exibirResultado("Cálculo de preços", false, "Dados de preços não encontrados");
        }
    } catch (Exception $e) {
        exibirResultado("Cálculo de preços", false, "Erro: " . $e->getMessage());
    }
    
    echo "<h2>6. Dados de Teste</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>Cartões de Teste Stripe:</h3>";
    echo "<ul>";
    echo "<li><strong>Sucesso:</strong> 4242 4242 4242 4242</li>";
    echo "<li><strong>Falha:</strong> 4000 0000 0000 0002</li>";
    echo "<li><strong>CVV:</strong> Qualquer 3 dígitos</li>";
    echo "<li><strong>Data:</strong> Qualquer data futura</li>";
    echo "</ul>";
    
    echo "<h3>Contas de Teste PayPal:</h3>";
    echo "<p>Use contas sandbox criadas no PayPal Developer</p>";
    echo "</div>";
    
    echo "<h2>7. Próximos Passos</h2>";
    
    echo "<ol>";
    echo "<li><a href='admin/configuracoes_pagamento.php'>Configurar chaves de API</a></li>";
    echo "<li><a href='loja2.php'>Testar criação de pedido</a></li>";
    echo "<li><a href='admin/pedidos.php'>Verificar pedidos no painel admin</a></li>";
    echo "</ol>";
    
    // Estatísticas do sistema
    echo "<h2>8. Estatísticas</h2>";
    
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM pedidos");
        $total_pedidos = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM pedidos WHERE status_pagamento = 'Pago'");
        $pedidos_pagos = $stmt->fetchColumn();
        
        $stmt = $pdo->query("SELECT SUM(preco_total) as total FROM pedidos WHERE status_pagamento = 'Pago'");
        $valor_total = $stmt->fetchColumn() ?: 0;
        
        echo "<div style='display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin: 15px 0;'>";
        echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; text-align: center;'>";
        echo "<h3 style='margin: 0; color: #28a745;'>$total_pedidos</h3>";
        echo "<p style='margin: 5px 0 0 0;'>Total de Pedidos</p>";
        echo "</div>";
        echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; text-align: center;'>";
        echo "<h3 style='margin: 0; color: #1976d2;'>$pedidos_pagos</h3>";
        echo "<p style='margin: 5px 0 0 0;'>Pedidos Pagos</p>";
        echo "</div>";
        echo "<div style='background: #fff3e0; padding: 15px; border-radius: 5px; text-align: center;'>";
        echo "<h3 style='margin: 0; color: #f57c00;'>€" . number_format($valor_total, 2, ',', '.') . "</h3>";
        echo "<p style='margin: 5px 0 0 0;'>Valor Total</p>";
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        exibirResultado("Estatísticas", false, "Erro ao obter estatísticas: " . $e->getMessage());
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<strong>❌ Erro durante os testes:</strong><br>";
    echo htmlspecialchars($e->getMessage());
    echo "</div>";
}
?>

<style>
    body { 
        font-family: Arial, sans-serif; 
        max-width: 900px; 
        margin: 0 auto; 
        padding: 20px; 
        background: #f5f5f5; 
    }
    h1 { 
        color: #93622B; 
        text-align: center; 
        background: white; 
        padding: 20px; 
        border-radius: 10px; 
        box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
    }
    h2 { 
        color: #333; 
        border-bottom: 2px solid #93622B; 
        padding-bottom: 5px; 
        margin-top: 30px; 
    }
    a { 
        color: #93622B; 
        text-decoration: none; 
    }
    a:hover { 
        text-decoration: underline; 
    }
    ol, ul { 
        line-height: 1.6; 
    }
</style>
