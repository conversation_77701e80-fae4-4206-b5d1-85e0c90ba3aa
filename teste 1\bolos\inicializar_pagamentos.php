<?php
/**
 * Script para inicializar rapidamente o sistema de pagamentos
 * Execute este script para criar as tabelas e configurações básicas
 */

include 'db.php';

echo "<h1>Inicialização Rápida do Sistema de Pagamentos</h1>";

try {
    // 1. Verificar e adicionar colunas à tabela pedidos
    echo "<h2>1. Atualizando tabela 'pedidos'...</h2>";
    
    $colunas_pedidos = [
        'preco_total' => "ALTER TABLE pedidos ADD COLUMN preco_total DECIMAL(10,2) DEFAULT 0.00",
        'status_pagamento' => "ALTER TABLE pedidos ADD COLUMN status_pagamento ENUM('Pendente', 'Processando', 'Pago', 'Falhado', 'Reembolsado') DEFAULT 'Pendente'",
        'metodo_pagamento' => "ALTER TABLE pedidos ADD COLUMN metodo_pagamento VARCHAR(50) NULL",
        'referencia_pagamento' => "ALTER TABLE pedidos ADD COLUMN referencia_pagamento VARCHAR(255) NULL",
        'data_pagamento' => "ALTER TABLE pedidos ADD COLUMN data_pagamento DATETIME NULL"
    ];
    
    foreach ($colunas_pedidos as $coluna => $sql) {
        try {
            // Verificar se a coluna já existe
            $stmt = $pdo->query("SHOW COLUMNS FROM pedidos LIKE '$coluna'");
            if ($stmt->rowCount() == 0) {
                $pdo->exec($sql);
                echo "✅ Coluna '$coluna' adicionada<br>";
            } else {
                echo "ℹ️ Coluna '$coluna' já existe<br>";
            }
        } catch (PDOException $e) {
            echo "⚠️ Erro ao adicionar coluna '$coluna': " . $e->getMessage() . "<br>";
        }
    }
    
    // 2. Criar tabela de configurações de pagamento
    echo "<h2>2. Criando tabela 'configuracoes_pagamento'...</h2>";
    
    try {
        $sql_config = "
        CREATE TABLE IF NOT EXISTS configuracoes_pagamento (
            id_config INT AUTO_INCREMENT PRIMARY KEY,
            metodo VARCHAR(50) NOT NULL UNIQUE,
            ativo BOOLEAN DEFAULT FALSE,
            configuracoes JSON NOT NULL,
            data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
            data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
        ";
        
        $pdo->exec($sql_config);
        echo "✅ Tabela 'configuracoes_pagamento' criada<br>";
        
    } catch (PDOException $e) {
        echo "ℹ️ Tabela 'configuracoes_pagamento' já existe<br>";
    }
    
    // 3. Inserir configurações padrão
    echo "<h2>3. Inserindo configurações padrão...</h2>";
    
    $configuracoes_padrao = [
        [
            'metodo' => 'stripe',
            'ativo' => 0,
            'configuracoes' => json_encode([
                'public_key' => '',
                'secret_key' => '',
                'webhook_secret' => ''
            ])
        ],
        [
            'metodo' => 'paypal',
            'ativo' => 0,
            'configuracoes' => json_encode([
                'client_id' => '',
                'client_secret' => '',
                'mode' => 'sandbox'
            ])
        ]
    ];
    
    foreach ($configuracoes_padrao as $config) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO configuracoes_pagamento (metodo, ativo, configuracoes) 
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                configuracoes = IF(configuracoes = '{}' OR configuracoes = '', VALUES(configuracoes), configuracoes)
            ");
            $stmt->execute([$config['metodo'], $config['ativo'], $config['configuracoes']]);
            echo "✅ Configuração para '{$config['metodo']}' inserida<br>";
        } catch (PDOException $e) {
            echo "ℹ️ Configuração para '{$config['metodo']}' já existe<br>";
        }
    }
    
    // 4. Criar tabela de pagamentos
    echo "<h2>4. Criando tabela 'pagamentos'...</h2>";
    
    try {
        $sql_pagamentos = "
        CREATE TABLE IF NOT EXISTS pagamentos (
            id_pagamento INT AUTO_INCREMENT PRIMARY KEY,
            id_pedido INT NOT NULL,
            valor DECIMAL(10,2) NOT NULL,
            metodo_pagamento VARCHAR(50) NOT NULL,
            status_pagamento ENUM('Pendente', 'Processando', 'Pago', 'Falhado', 'Reembolsado') DEFAULT 'Pendente',
            referencia_externa VARCHAR(255) NULL,
            dados_pagamento JSON NULL,
            data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
            data_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (id_pedido) REFERENCES pedidos(id_pedido) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci
        ";
        
        $pdo->exec($sql_pagamentos);
        echo "✅ Tabela 'pagamentos' criada<br>";
        
    } catch (PDOException $e) {
        echo "ℹ️ Tabela 'pagamentos' já existe<br>";
    }
    
    // 5. Atualizar pedidos existentes com preço total
    echo "<h2>5. Atualizando pedidos existentes...</h2>";
    
    try {
        $sql_update = "
        UPDATE pedidos p
        JOIN bolo b ON p.id_bolo = b.id_bolo
        JOIN tipo_bolo tb ON b.id_tipo = tb.id_tipo
        LEFT JOIN pesos pe ON b.id_peso = pe.id_peso
        LEFT JOIN decoracao d ON b.id_decoracao = d.id_decoracao
        SET p.preco_total = COALESCE(tb.preco, 0) + COALESCE(pe.preco, 0) + COALESCE(d.preco, 0)
        WHERE p.preco_total = 0.00 OR p.preco_total IS NULL
        ";
        
        $stmt = $pdo->prepare($sql_update);
        $stmt->execute();
        $updated = $stmt->rowCount();
        echo "✅ $updated pedidos atualizados com preço total<br>";
        
    } catch (PDOException $e) {
        echo "⚠️ Erro ao atualizar pedidos: " . $e->getMessage() . "<br>";
    }
    
    // 6. Verificar status final
    echo "<h2>6. Status Final</h2>";
    
    // Verificar configurações
    $stmt = $pdo->query("SELECT metodo, ativo FROM configuracoes_pagamento");
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Métodos de Pagamento:</h3>";
    echo "<ul>";
    foreach ($configs as $config) {
        $status = $config['ativo'] ? "✅ Ativo" : "❌ Inativo";
        echo "<li>" . ucfirst($config['metodo']) . ": $status</li>";
    }
    echo "</ul>";
    
    // Verificar pedidos
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM pedidos");
    $total_pedidos = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM pedidos WHERE preco_total > 0");
    $pedidos_com_preco = $stmt->fetchColumn();
    
    echo "<h3>Pedidos:</h3>";
    echo "<ul>";
    echo "<li>Total de pedidos: $total_pedidos</li>";
    echo "<li>Pedidos com preço: $pedidos_com_preco</li>";
    echo "</ul>";
    
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>✅ Inicialização Concluída!</h3>";
    echo "<p>O sistema de pagamentos foi inicializado com sucesso. Próximos passos:</p>";
    echo "<ol>";
    echo "<li><a href='admin/configuracoes_pagamento.php' style='color: #155724;'>Configurar chaves API do Stripe/PayPal</a></li>";
    echo "<li><a href='loja2.php' style='color: #155724;'>Testar criação de pedido</a></li>";
    echo "<li><a href='testar_sistema_pagamento.php' style='color: #155724;'>Executar testes do sistema</a></li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>❌ Erro durante a inicialização:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<style>
    body { 
        font-family: Arial, sans-serif; 
        max-width: 800px; 
        margin: 0 auto; 
        padding: 20px; 
        background: #f5f5f5; 
    }
    h1 { 
        color: #93622B; 
        text-align: center; 
        background: white; 
        padding: 20px; 
        border-radius: 10px; 
        box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
    }
    h2 { 
        color: #333; 
        border-bottom: 2px solid #93622B; 
        padding-bottom: 5px; 
        margin-top: 30px; 
    }
    h3 { 
        color: #555; 
        margin-top: 20px; 
    }
    a { 
        color: #93622B; 
        text-decoration: none; 
    }
    a:hover { 
        text-decoration: underline; 
    }
    ul, ol { 
        line-height: 1.6; 
    }
</style>
