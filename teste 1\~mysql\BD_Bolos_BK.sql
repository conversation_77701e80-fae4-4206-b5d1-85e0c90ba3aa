CREATE DATABASE  IF NOT EXISTS `loja_bolos` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
USE `loja_bolos`;
-- MySQL dump 10.13  Distrib 8.0.41, for Win64 (x86_64)
--
-- Host: localhost    Database: loja_bolos
-- ------------------------------------------------------
-- Server version	8.0.41

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `adicionais`
--

DROP TABLE IF EXISTS `adicionais`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `adicionais` (
  `id_adicional` int NOT NULL AUTO_INCREMENT,
  `nome` varchar(50) NOT NULL,
  `preco` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id_adicional`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `adicionais`
--

LOCK TABLES `adicionais` WRITE;
/*!40000 ALTER TABLE `adicionais` DISABLE KEYS */;
INSERT INTO `adicionais` VALUES (1,'Frutas Frescas',3.00),(2,'Nozes e Amêndoas',3.50),(3,'Pérolas de Açúcar',2.50),(4,'Raspas de Chocolate',3.00),(5,'Marshmallows',2.00),(6,'Pedaços de Oreo',3.25),(7,'Crocante de Caramelo',3.75),(8,'Confetes Coloridos',2.80),(9,'Pistache Triturado',4.50),(10,'Gotas de Chocolate',3.30);
/*!40000 ALTER TABLE `adicionais` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `bolo`
--

DROP TABLE IF EXISTS `bolo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bolo` (
  `id_bolo` int NOT NULL AUTO_INCREMENT,
  `id_tipo` int NOT NULL,
  `id_massa` int DEFAULT NULL,
  `id_cobertura` int DEFAULT NULL,
  `id_recheio` int DEFAULT NULL,
  `id_peso` int DEFAULT NULL,
  `id_formato` int DEFAULT NULL,
  `id_adicional` int DEFAULT NULL,
  `id_decoracao` int DEFAULT NULL,
  PRIMARY KEY (`id_bolo`),
  KEY `id_tipo` (`id_tipo`),
  KEY `id_massa` (`id_massa`),
  KEY `id_cobertura` (`id_cobertura`),
  KEY `id_recheio` (`id_recheio`),
  KEY `id_peso` (`id_peso`),
  KEY `id_formato` (`id_formato`),
  KEY `id_adicional` (`id_adicional`),
  KEY `id_decoracao` (`id_decoracao`),
  CONSTRAINT `bolo_ibfk_1` FOREIGN KEY (`id_tipo`) REFERENCES `tipo_bolo` (`id_tipo`),
  CONSTRAINT `bolo_ibfk_2` FOREIGN KEY (`id_massa`) REFERENCES `massas` (`id_massa`),
  CONSTRAINT `bolo_ibfk_3` FOREIGN KEY (`id_cobertura`) REFERENCES `coberturas` (`id_cobertura`),
  CONSTRAINT `bolo_ibfk_4` FOREIGN KEY (`id_recheio`) REFERENCES `recheios` (`id_recheio`),
  CONSTRAINT `bolo_ibfk_5` FOREIGN KEY (`id_peso`) REFERENCES `pesos` (`id_peso`),
  CONSTRAINT `bolo_ibfk_6` FOREIGN KEY (`id_formato`) REFERENCES `formatos` (`id_formato`),
  CONSTRAINT `bolo_ibfk_7` FOREIGN KEY (`id_adicional`) REFERENCES `adicionais` (`id_adicional`),
  CONSTRAINT `bolo_ibfk_8` FOREIGN KEY (`id_decoracao`) REFERENCES `decoracao` (`id_decoracao`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `bolo`
--

LOCK TABLES `bolo` WRITE;
/*!40000 ALTER TABLE `bolo` DISABLE KEYS */;
/*!40000 ALTER TABLE `bolo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `coberturas`
--

DROP TABLE IF EXISTS `coberturas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `coberturas` (
  `id_cobertura` int NOT NULL AUTO_INCREMENT,
  `nome` varchar(50) NOT NULL,
  PRIMARY KEY (`id_cobertura`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `coberturas`
--

LOCK TABLES `coberturas` WRITE;
/*!40000 ALTER TABLE `coberturas` DISABLE KEYS */;
INSERT INTO `coberturas` VALUES (1,'Ganache de Chocolate'),(2,'Creme de Manteiga'),(3,'Chantilly'),(4,'Fondant'),(5,'Doce de Ovos'),(6,'Merengue Italiano'),(7,'Chocolate Meio Amargo'),(8,'Mousse de Morango'),(9,'Creme de Avelã'),(10,'Brigadeiro Branco'),(11,'Doce de leite');
/*!40000 ALTER TABLE `coberturas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `decoracao`
--

DROP TABLE IF EXISTS `decoracao`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `decoracao` (
  `id_decoracao` int NOT NULL AUTO_INCREMENT,
  `nome_decoracao` varchar(50) NOT NULL,
  `preco` decimal(10,2) NOT NULL,
  `imagem` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id_decoracao`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `decoracao`
--

LOCK TABLES `decoracao` WRITE;
/*!40000 ALTER TABLE `decoracao` DISABLE KEYS */;
INSERT INTO `decoracao` VALUES (1,'Flores comestíveis',5.00,'flores.jpg'),(2,'Topper personalizado',4.50,'topper.jpg'),(3,'Glitter comestível',3.00,'glitter.jpg'),(4,'Chantilly',3.50,'chantilly.jpg'),(5,'Pintura artística comestível',6.50,'pintura.jpg'),(6,'Efeito mármore',5.75,'marmore.jpg'),(7,'Aerografia colorida',6.80,'aerografia.jpg'),(8,'Aplicação de folhas de ouro',7.00,'ouro.jpg'),(9,'Drip Cake',5.90,'dripcake.jpg'),(10,'Stencil com coberturas',4.20,'stencil.jpg'),(11,'Decoração temática 3D',7.50,'tema3d.jpg');
/*!40000 ALTER TABLE `decoracao` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `formatos`
--

DROP TABLE IF EXISTS `formatos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `formatos` (
  `id_formato` int NOT NULL AUTO_INCREMENT,
  `nome` varchar(50) NOT NULL,
  PRIMARY KEY (`id_formato`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `formatos`
--

LOCK TABLES `formatos` WRITE;
/*!40000 ALTER TABLE `formatos` DISABLE KEYS */;
INSERT INTO `formatos` VALUES (1,'Redondo'),(2,'Quadrado'),(3,'Retangular'),(4,'Coração');
/*!40000 ALTER TABLE `formatos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `massas`
--

DROP TABLE IF EXISTS `massas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `massas` (
  `id_massa` int NOT NULL AUTO_INCREMENT,
  `nome` varchar(50) NOT NULL,
  PRIMARY KEY (`id_massa`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `massas`
--

LOCK TABLES `massas` WRITE;
/*!40000 ALTER TABLE `massas` DISABLE KEYS */;
INSERT INTO `massas` VALUES (1,'Pão de Ló'),(2,'Simples'),(3,'Chocolate'),(4,'Baunilha'),(5,'Red Velvet'),(6,'Cenoura'),(7,'Nozes'),(8,'Laranja'),(9,'Coco'),(10,'Marmore'),(11,'Canela'),(12,'Limão'),(13,'Café');
/*!40000 ALTER TABLE `massas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pedidos`
--

DROP TABLE IF EXISTS `pedidos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pedidos` (
  `id_pedido` int NOT NULL AUTO_INCREMENT,
  `id_utilizador` int NOT NULL,
  `id_bolo` int NOT NULL,
  `data_pedido` datetime DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(50) NOT NULL DEFAULT 'Em Processamento',
  PRIMARY KEY (`id_pedido`),
  KEY `id_utilizador` (`id_utilizador`),
  KEY `id_bolo` (`id_bolo`),
  CONSTRAINT `pedidos_ibfk_1` FOREIGN KEY (`id_utilizador`) REFERENCES `utilizadores` (`id_utilizador`),
  CONSTRAINT `pedidos_ibfk_2` FOREIGN KEY (`id_bolo`) REFERENCES `bolo` (`id_bolo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pedidos`
--

LOCK TABLES `pedidos` WRITE;
/*!40000 ALTER TABLE `pedidos` DISABLE KEYS */;
/*!40000 ALTER TABLE `pedidos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pesos`
--

DROP TABLE IF EXISTS `pesos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pesos` (
  `id_peso` int NOT NULL AUTO_INCREMENT,
  `peso` varchar(10) NOT NULL,
  `preco` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id_peso`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pesos`
--

LOCK TABLES `pesos` WRITE;
/*!40000 ALTER TABLE `pesos` DISABLE KEYS */;
INSERT INTO `pesos` VALUES (1,'1kg',1.00),(2,'1.5kg',1.50),(3,'2kg',2.00),(4,'2.5kg',2.50),(5,'3kg',3.00),(6,'4kg',4.00),(7,'5kg',5.00),(8,'6kg',6.00),(9,'7kg',7.00),(10,'8kg',8.00),(11,'9kg',9.00),(12,'10kg',10.00);
/*!40000 ALTER TABLE `pesos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `recheios`
--

DROP TABLE IF EXISTS `recheios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `recheios` (
  `id_recheio` int NOT NULL AUTO_INCREMENT,
  `nome` varchar(50) NOT NULL,
  PRIMARY KEY (`id_recheio`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `recheios`
--

LOCK TABLES `recheios` WRITE;
/*!40000 ALTER TABLE `recheios` DISABLE KEYS */;
INSERT INTO `recheios` VALUES (1,'Brigadeiro'),(2,'Creme de Ovos'),(3,'Doce de Leite'),(4,'Geleia de frutos vermelhos'),(5,'Chocolate Branco'),(6,'Mousse de Maracujá'),(7,'Caramelo Salgado'),(8,'Limão Siciliano'),(9,'Coco e Leite Condensado'),(10,'Nutella'),(11,'Tiramisu'),(12,'Crocante');
/*!40000 ALTER TABLE `recheios` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tipo_bolo`
--

DROP TABLE IF EXISTS `tipo_bolo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tipo_bolo` (
  `id_tipo` int NOT NULL AUTO_INCREMENT,
  `nome_tipo` varchar(50) NOT NULL,
  `preco` decimal(10,2) NOT NULL,
  PRIMARY KEY (`id_tipo`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tipo_bolo`
--

LOCK TABLES `tipo_bolo` WRITE;
/*!40000 ALTER TABLE `tipo_bolo` DISABLE KEYS */;
INSERT INTO `tipo_bolo` VALUES (1,'Bolo Simples',10.00),(2,'Bolo com Cobertura',15.00),(3,'Bolo Personalizado',25.00),(4,'Bolo com Recheio',16.00),(5,'Bolo Simples',10.00),(6,'Bolo com Cobertura',15.00),(7,'Bolo Personalizado',20.00),(8,'Bolo com Recheio',16.00);
/*!40000 ALTER TABLE `tipo_bolo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `utilizadores`
--

DROP TABLE IF EXISTS `utilizadores`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `utilizadores` (
  `id_utilizador` int NOT NULL AUTO_INCREMENT,
  `nome` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `senha` varchar(255) NOT NULL,
  `telefone` varchar(20) DEFAULT NULL,
  `morada` varchar(150) DEFAULT NULL,
  `codigo_postal` varchar(20) DEFAULT NULL,
  `role` varchar(20) NOT NULL DEFAULT 'cliente', -- Novo campo para definir o tipo de usuário
  `data_registo` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_utilizador`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `utilizadores`
--

LOCK TABLES `utilizadores` WRITE;
/*!40000 ALTER TABLE `utilizadores` DISABLE KEYS */;
/*!40000 ALTER TABLE `utilizadores` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-08 10:18:41
