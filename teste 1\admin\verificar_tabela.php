<?php
include '../db.php';

try {
    // Verificar se a coluna 'role' existe na tabela utilizadores
    $stmt = $pdo->query("DESCRIBE utilizadores");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>Colunas existentes na tabela 'utilizadores':</h2>";
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>" . htmlspecialchars($column) . "</li>";
    }
    echo "</ul>";
    
    // Verificar se a coluna 'role' existe
    $role_exists = false;
    foreach ($columns as $column) {
        if (strtolower($column) === 'role') {
            $role_exists = true;
            break;
        }
    }
    
    if (!$role_exists) {
        echo "<h3>Adicionando coluna 'role' à tabela utilizadores...</h3>";
        
        // Adicionar a coluna role
        $pdo->exec("ALTER TABLE utilizadores ADD COLUMN role VARCHAR(20) DEFAULT 'cliente'");
        
        echo "<p style='color: green;'>Coluna 'role' adicionada com sucesso!</p>";
        
        // Criar um usuário admin padrão se não existir
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM utilizadores WHERE email = ?");
        $stmt->execute(['<EMAIL>']);
        $admin_exists = $stmt->fetchColumn();
        
        if ($admin_exists == 0) {
            echo "<h3>Criando usuário administrador padrão...</h3>";
            
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO utilizadores (nome, email, senha, role) VALUES (?, ?, ?, ?)");
            $stmt->execute(['Administrador', '<EMAIL>', $admin_password, 'admin']);
            
            echo "<p style='color: green;'>Usuário administrador criado!</p>";
            echo "<p><strong>Email:</strong> <EMAIL></p>";
            echo "<p><strong>Senha:</strong> admin123</p>";
        }
    } else {
        echo "<p style='color: blue;'>A coluna 'role' já existe na tabela.</p>";
        
        // Verificar se existe um admin
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM utilizadores WHERE role = 'admin'");
        $stmt->execute();
        $admin_count = $stmt->fetchColumn();
        
        if ($admin_count == 0) {
            echo "<h3>Criando usuário administrador padrão...</h3>";
            
            $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO utilizadores (nome, email, senha, role) VALUES (?, ?, ?, ?)");
            $stmt->execute(['Administrador', '<EMAIL>', $admin_password, 'admin']);
            
            echo "<p style='color: green;'>Usuário administrador criado!</p>";
            echo "<p><strong>Email:</strong> <EMAIL></p>";
            echo "<p><strong>Senha:</strong> admin123</p>";
        } else {
            echo "<p style='color: blue;'>Já existe pelo menos um administrador no sistema.</p>";
        }
    }
    
} catch(PDOException $e) {
    die("<p style='color: red;'>Erro: " . $e->getMessage() . "</p>");
}
?>

<p><a href="dashboard.php" style="display: inline-block; padding: 10px 20px; background-color: #93622B; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px;">Ir para o Dashboard</a></p>
<p><a href="../bolos/login.php" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-top: 10px;">Fazer Login</a></p>
