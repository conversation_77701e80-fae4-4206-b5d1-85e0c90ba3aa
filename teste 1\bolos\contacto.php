<?php
require_once 'db.php';
session_start();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
     <!-- Font Awesome -->
     <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courgette&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Libre+Baskerville:wght@400;700&display=swap" rel="stylesheet">

     <!-- Google Fonts cake garden-->
     <link href="https://fonts.googleapis.com/css2?family=Playfair+Display&display=swap" rel="stylesheet">
     <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400..700&display=swap" rel="stylesheet">

     <!--H1 fonts-->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300..700;1,300..700&display=swap" rel="stylesheet">

     <!--H2 fonts-->
     <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lobster+Two:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
      <!-- CSS -->
      <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Complete form with reCAPTCHA and custom fields -->
<form action="https://api.staticforms.xyz/submit" method="POST">
  <!-- Required: Your Static Forms API key -->
  <input type="hidden" name="apiKey" value="sf_9a8kg7g1ghad305n8f2i3en4">

  <!-- Enable reply-to functionality -->
  <input type="hidden" name="replyTo" value="@">

  <!-- Anti-spam honeypot field -->
  <input
    type="text"
    name="honeypot"
    style="display: none"
    tabindex="-1"
    autocomplete="off"
  >

    <!--navbar-->
    <header>
            <div class="barra">
                <div class="logo">
                <img src="../image/Cake_Garden__3_-removebg-preview.png" alt="Cake Garden Logo">
                </div>
                <!--barra de pesquisa-->
                <div class="search-container">
                    <input type="search" class="search-input" placeholder="search">
                    <button class="search-button">
                    <i class="fas fa-search"></i>
                    </button>
                </div>
                 <!--fim da barra de pesquisa-->
                    <ul class="nav-list-icon">    
                        <i class="fa-solid fa-cart-plus"></i>
                        <?php if(isset($_SESSION['user_id'])): ?>
                            <div class="user-initial">
                                <span><?php echo strtoupper(substr($_SESSION['user_name'], 0, 1)); ?></span>
                            </div>
                            <a href="logout.php" class="logout-btn">
                                <i class="fas fa-sign-out-alt"></i> sair
                            </a>
                        <?php else: ?>
                            <a href="./login.php">
                                <i class="fa-solid fa-user"></i>
                            </a>
                        <?php endif; ?>
                    </ul>
            </div>
               <!--menu-->
            <div class="menu-bar">
                <ul>
                    <li><a href="index.php"><i class="fa-solid fa-house"></i>INÍCIO</a></li>
                    <li><a href="about.php"><i class="fa-solid fa-handshake"></i>SOBRE</a></li>
                    <li><a href="loja.php"><i class="fa-solid fa-bag-shopping"></i>LOJA</a></li>
                    <li style="width: 150px;"><a href="contacto.php"><i class="fa-solid fa-phone"></i>CONTACTO</a></li>
                    <li><a href="#"><i class="fa-solid fa-pen-to-square"></i>BLOG</a></li>
                    <li><a href="galeria.php" class="active"><i class="fa-solid fa-images"></i>GALERIA</a></li>
                    <li style="width: 150px;"><a href="clinente.php"><i class="fa-solid fa-star"></i>CLIENTE</a></li>
                </ul>
                <!--fim do menu-->
            </div>
    </header>
    <header>
        <div class="about-us">
          
            <div class="decorative-line">
            <div class="line"></div> 
              <h1>FALE CONNOSCO</h1>
            <div class="line"></div>

        </div>
        <!--formulário de contacto-->
        <Section class="contacto">
            <div class="contact-container">
                <div class="contact-column">
                    <h3>Horário de funcionamento</h3>
                    <p>Segunda a sexta: 8:00 - 18:00</p>
                    <p>Sábado: 9:00 - 15:00</p>
                    <p>Domingo: Fechado</p>
                    
                    <!-- Aviso de antecedência para encomendas -->
                    <div class="aviso-encomenda">
                        <i class="fa-solid fa-circle-exclamation"></i>
                        <h4>Aviso Importante</h4>
                        <p>Todas as encomendas devem ser feitas com pelo menos 2 dias de antecedência para garantirmos a qualidade e personalização dos nossos produtos.</p>
                    </div>
                </div>
                
                <!-- Formulário de contato usando Static Forms -->
                <div class="contact-column">
                    <h3>Envie mensagem</h3>
                    <form class="form" action="https://api.staticforms.xyz/submit" method="POST">
                        <!-- Required: Your Static Forms API key -->
                        <input type="hidden" name="accessKey" value="sf_9a8kg7g1ghad305n8f2i3en4">
                        
                        <!-- Redirect URL after form submission -->
                        <input type="hidden" name="redirectTo" value="http://localhost/teste%201/bolos/obrigado.php">
                        
                        <!-- Subject line for the email -->
                        <input type="hidden" name="subject" value="Novo contato do site Cake Garden">
                        
                        <!-- Enable reply-to functionality - Corrigido -->
                        <!-- O campo replyTo será preenchido automaticamente com o email do remetente -->
                        
                        <!-- Anti-spam honeypot field -->
                        <input type="text" name="honeypot" style="display: none" tabindex="-1" autocomplete="off">
                        
                        <!-- Form fields - Certifique-se de que os nomes dos campos estão corretos -->
                        <input class="field" name="name" placeholder="Nome" required>
                        <input class="field" name="email" placeholder="E-mail" required>
                        <textarea name="message" class="message" placeholder="Digite sua mensagem aqui" required></textarea>
                        
                        <div class="button-container">
                            <button type="submit">Enviar</button>
                        </div>
                    </form>
                </div>

                <div class="contact-column">
                    <h3>Nossos contactos</h3>
                    <p><i class="fa-solid fa-mobile"></i> +351 912 345 678</p>
                    <p><i class="fa-solid fa-envelope"></i> <EMAIL></p>
                    <p><i class="fa-solid fa-location-dot"></i> Av. da Liberdade, 123, Lisboa</p>
                </div>
            </div>
        </Section>

         <!--footer-->
       <footer>
    <div class="footer_content">
        <!-- Seção de contato -->
        <div class="footer_section footer_contactos">
            <h3><i class="fa-solid fa-phone"></i> CONTACTOS</h3>
            <ul>
                <li><a href="#"><i class="fa-solid fa-mobile"></i> +351 912 345 678</a></li>
                <li><a href="#"><i class="fa-solid fa-envelope"></i> <EMAIL></a></li>
                <li><a href="#"><i class="fa-solid fa-location-dot"></i> Av. da Liberdade, 123, Lisboa</a></li>
            </ul>
        </div>

        <!-- Seção de entrega -->
        <div class="footer_section footer_entrega">
            <h3><i class="fa-solid fa-truck"></i> ENTREGA RÁPIDA</h3>
            
                 <ul>
                    <li><a href="prazo_entrega.php">Prazo de entrega e Áreas atendidas</a></li>
                </ul>
           
        </div>

        <!-- Seção de redes sociais -->
        <div class="footer_section footer_social">
            <h3><i class="fa-solid fa-globe"></i> SOCIAL</h3>
            <ul>
                <li><a href="#"><i class="fa-brands fa-facebook"></i></a></li>
                <li><a href="#"><i class="fa-brands fa-instagram"></i></a></li>
                <li><a href="#"><i class="fa-brands fa-twitter"></i></a></li>
            </ul>
        </div>
        </div>

    <!-- Copyright -->
    <div class="footer_copyright">
        &copy; 2025 Cake Garden. Todos os direitos reservados.
    </div>
</footer>
            
</body>
</html>
