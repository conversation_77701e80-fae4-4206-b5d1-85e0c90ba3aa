<?php
session_start();
include 'db.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// Verificar se os dados foram enviados via POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: index.php");
    exit;
}

$pedido_id = $_POST['pedido_id'] ?? null;
$metodo_pagamento = $_POST['metodo_pagamento'] ?? null;
$user_id = $_SESSION['user_id'];

if (!$pedido_id || !$metodo_pagamento) {
    header("Location: index.php");
    exit;
}

try {
    // Buscar informações do pedido
    $stmt = $pdo->prepare("
        SELECT p.*, u.nome as cliente_nome, u.email as cliente_email
        FROM pedidos p
        JOIN utilizadores u ON p.id_utilizador = u.id_utilizador
        WHERE p.id_pedido = ? AND p.id_utilizador = ?
    ");
    $stmt->execute([$pedido_id, $user_id]);
    $pedido = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$pedido) {
        throw new Exception("Pedido não encontrado");
    }
    
    // Verificar se o pedido já foi pago
    if ($pedido['status_pagamento'] === 'Pago') {
        header("Location: pedido_sucesso.php?pedido_id=" . $pedido_id);
        exit;
    }
    
    // Buscar configurações do método de pagamento
    $stmt = $pdo->prepare("SELECT configuracoes FROM configuracoes_pagamento WHERE metodo = ? AND ativo = TRUE");
    $stmt->execute([$metodo_pagamento]);
    $config_row = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$config_row) {
        throw new Exception("Método de pagamento não disponível");
    }
    
    $config = json_decode($config_row['configuracoes'], true);
    
    // Criar registro de pagamento
    $stmt = $pdo->prepare("
        INSERT INTO pagamentos (id_pedido, valor, metodo_pagamento, status_pagamento)
        VALUES (?, ?, ?, 'Processando')
    ");
    $stmt->execute([$pedido_id, $pedido['preco_total'], $metodo_pagamento]);
    $pagamento_id = $pdo->lastInsertId();
    
    // Atualizar status do pedido
    $stmt = $pdo->prepare("UPDATE pedidos SET status_pagamento = 'Processando', metodo_pagamento = ? WHERE id_pedido = ?");
    $stmt->execute([$metodo_pagamento, $pedido_id]);
    
    // Processar pagamento baseado no método escolhido
    switch ($metodo_pagamento) {
        case 'stripe':
            $resultado = processarStripe($pedido, $config, $pagamento_id);
            break;
        case 'paypal':
            $resultado = processarPayPal($pedido, $config, $pagamento_id);
            break;
        default:
            throw new Exception("Método de pagamento não suportado");
    }
    
    if ($resultado['sucesso']) {
        // Atualizar pagamento como pago
        $stmt = $pdo->prepare("
            UPDATE pagamentos 
            SET status_pagamento = 'Pago', referencia_externa = ?, dados_pagamento = ?
            WHERE id_pagamento = ?
        ");
        $stmt->execute([
            $resultado['referencia'],
            json_encode($resultado['dados']),
            $pagamento_id
        ]);
        
        // Atualizar pedido
        $stmt = $pdo->prepare("
            UPDATE pedidos 
            SET status_pagamento = 'Pago', referencia_pagamento = ?, data_pagamento = NOW()
            WHERE id_pedido = ?
        ");
        $stmt->execute([$resultado['referencia'], $pedido_id]);
        
        // Redirecionar para página de sucesso
        header("Location: pedido_sucesso.php?pedido_id=" . $pedido_id);
        exit;
    } else {
        // Atualizar pagamento como falhado
        $stmt = $pdo->prepare("
            UPDATE pagamentos 
            SET status_pagamento = 'Falhado', dados_pagamento = ?
            WHERE id_pagamento = ?
        ");
        $stmt->execute([json_encode($resultado['erro']), $pagamento_id]);
        
        // Atualizar pedido
        $stmt = $pdo->prepare("UPDATE pedidos SET status_pagamento = 'Falhado' WHERE id_pedido = ?");
        $stmt->execute([$pedido_id]);
        
        // Redirecionar para página de erro
        header("Location: pedido_erro.php?erro=" . urlencode($resultado['erro']['message']));
        exit;
    }
    
} catch (Exception $e) {
    error_log("Erro no processamento de pagamento: " . $e->getMessage());
    header("Location: pedido_erro.php?erro=" . urlencode("Erro interno do servidor"));
    exit;
}

function processarStripe($pedido, $config, $pagamento_id) {
    require_once 'vendor/autoload.php'; // Assumindo que o Stripe SDK foi instalado via Composer
    
    try {
        \Stripe\Stripe::setApiKey($config['secret_key']);
        
        // Criar Payment Intent
        $intent = \Stripe\PaymentIntent::create([
            'amount' => $pedido['preco_total'] * 100, // Stripe usa centavos
            'currency' => 'eur',
            'metadata' => [
                'pedido_id' => $pedido['id_pedido'],
                'pagamento_id' => $pagamento_id,
                'cliente_email' => $pedido['cliente_email']
            ],
            'description' => 'Pedido #' . $pedido['id_pedido'] . ' - Cake Garden'
        ]);
        
        return [
            'sucesso' => true,
            'referencia' => $intent->id,
            'dados' => [
                'payment_intent_id' => $intent->id,
                'client_secret' => $intent->client_secret
            ]
        ];
        
    } catch (\Stripe\Exception\CardException $e) {
        return [
            'sucesso' => false,
            'erro' => [
                'type' => 'card_error',
                'message' => $e->getError()->message
            ]
        ];
    } catch (\Stripe\Exception\RateLimitException $e) {
        return [
            'sucesso' => false,
            'erro' => [
                'type' => 'rate_limit',
                'message' => 'Muitas tentativas. Tente novamente em alguns minutos.'
            ]
        ];
    } catch (\Stripe\Exception\InvalidRequestException $e) {
        return [
            'sucesso' => false,
            'erro' => [
                'type' => 'invalid_request',
                'message' => 'Dados de pagamento inválidos'
            ]
        ];
    } catch (\Stripe\Exception\AuthenticationException $e) {
        return [
            'sucesso' => false,
            'erro' => [
                'type' => 'authentication',
                'message' => 'Erro de configuração do pagamento'
            ]
        ];
    } catch (\Stripe\Exception\ApiConnectionException $e) {
        return [
            'sucesso' => false,
            'erro' => [
                'type' => 'network',
                'message' => 'Erro de conexão. Tente novamente.'
            ]
        ];
    } catch (\Stripe\Exception\ApiErrorException $e) {
        return [
            'sucesso' => false,
            'erro' => [
                'type' => 'api_error',
                'message' => 'Erro no processamento do pagamento'
            ]
        ];
    }
}

function processarPayPal($pedido, $config, $pagamento_id) {
    // Implementação básica do PayPal
    // Em produção, você usaria o SDK oficial do PayPal
    
    $paypal_url = $config['mode'] === 'sandbox' 
        ? 'https://api.sandbox.paypal.com' 
        : 'https://api.paypal.com';
    
    try {
        // Obter token de acesso
        $token_response = obterTokenPayPal($config, $paypal_url);
        
        if (!$token_response['sucesso']) {
            return $token_response;
        }
        
        $access_token = $token_response['token'];
        
        // Criar pagamento
        $payment_data = [
            'intent' => 'CAPTURE',
            'purchase_units' => [[
                'amount' => [
                    'currency_code' => 'EUR',
                    'value' => number_format($pedido['preco_total'], 2, '.', '')
                ],
                'description' => 'Pedido #' . $pedido['id_pedido'] . ' - Cake Garden'
            ]],
            'application_context' => [
                'return_url' => 'https://seusite.com/paypal_success.php?pedido_id=' . $pedido['id_pedido'],
                'cancel_url' => 'https://seusite.com/paypal_cancel.php?pedido_id=' . $pedido['id_pedido']
            ]
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $paypal_url . '/v2/checkout/orders');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payment_data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $access_token
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code === 201) {
            $payment_response = json_decode($response, true);
            
            return [
                'sucesso' => true,
                'referencia' => $payment_response['id'],
                'dados' => $payment_response
            ];
        } else {
            return [
                'sucesso' => false,
                'erro' => [
                    'type' => 'paypal_error',
                    'message' => 'Erro ao criar pagamento PayPal'
                ]
            ];
        }
        
    } catch (Exception $e) {
        return [
            'sucesso' => false,
            'erro' => [
                'type' => 'exception',
                'message' => $e->getMessage()
            ]
        ];
    }
}

function obterTokenPayPal($config, $paypal_url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $paypal_url . '/v1/oauth2/token');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, 'grant_type=client_credentials');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_USERPWD, $config['client_id'] . ':' . $config['client_secret']);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: application/json',
        'Accept-Language: en_US'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        $token_data = json_decode($response, true);
        return [
            'sucesso' => true,
            'token' => $token_data['access_token']
        ];
    } else {
        return [
            'sucesso' => false,
            'erro' => [
                'type' => 'token_error',
                'message' => 'Erro ao obter token PayPal'
            ]
        ];
    }
}
?>
