<?php
require_once 'db.php'; // Inclui a conexão com o banco de dados

try {
    // Verificar se a tabela já existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'depoimentos'");
    $table_exists = $stmt->rowCount() > 0;
    
    if (!$table_exists) {
        // Criar a tabela depoimentos com sintaxe corrigida
        $sql = "CREATE TABLE depoimentos (
            id_depoimento INT AUTO_INCREMENT PRIMARY KEY,
            nome VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            comentario TEXT NOT NULL,
            foto VARCHAR(255) DEFAULT '../image/cliente-default.jpg',
            avaliacao INT DEFAULT 5,
            data DATE DEFAULT (CURRENT_DATE),
            status VARCHAR(20) DEFAULT 'pendente'
        )";
        
        $pdo->exec($sql);
        echo "<p style='color: green;'>Tabela 'depoimentos' criada com sucesso!</p>";
        
        // Inserir alguns depoimentos de exemplo
        $depoimentos = [
            [
                'nome' => 'Maria Silva',
                'email' => '<EMAIL>',
                'comentario' => 'O bolo de chocolate com morango que encomendei para o aniversário da minha filha estava simplesmente divino! Todos os convidados elogiaram o sabor e a decoração.',
                'foto' => '../image/cliente1.jpg',
                'avaliacao' => 5,
                'data' => '2023-12-15',
                'status' => 'aprovado'
            ],
            [
                'nome' => 'João Pereira',
                'email' => '<EMAIL>',
                'comentario' => 'Encomendei um bolo para o aniversário da minha esposa e fiquei impressionado com a qualidade. A massa estava fofa e o recheio de doce de leite perfeito!',
                'foto' => '../image/cliente2.jpg',
                'avaliacao' => 5,
                'data' => '2023-11-20',
                'status' => 'aprovado'
            ],
            [
                'nome' => 'Ana Oliveira',
                'email' => '<EMAIL>',
                'comentario' => 'Já experimentei vários bolos da Cake Garden e nunca me decepcionei. O atendimento é excelente e os bolos são sempre frescos e deliciosos.',
                'foto' => '../image/cliente3.jpg',
                'avaliacao' => 4,
                'data' => '2023-10-05',
                'status' => 'aprovado'
            ],
            [
                'nome' => 'Carlos Santos',
                'email' => '<EMAIL>',
                'comentario' => 'O bolo de casamento que encomendamos superou todas as nossas expectativas! Além de lindo, estava delicioso e todos os convidados adoraram.',
                'foto' => '../image/cliente4.jpg',
                'avaliacao' => 5,
                'data' => '2023-09-18',
                'status' => 'aprovado'
            ],
            [
                'nome' => 'Mariana Costa',
                'email' => '<EMAIL>',
                'comentario' => 'Adoro os bolos da Cake Garden! Sempre que tenho uma ocasião especial, faço questão de encomendar com eles. Nunca me decepcionaram.',
                'foto' => '../image/cliente5.jpg',
                'avaliacao' => 5,
                'data' => '2023-08-30',
                'status' => 'aprovado'
            ],
            [
                'nome' => 'Pedro Almeida',
                'email' => '<EMAIL>',
                'comentario' => 'Encomendei um bolo para o aniversário da empresa e foi um sucesso! Todos elogiaram o sabor e a apresentação. Com certeza voltarei a encomendar.',
                'foto' => '../image/cliente6.jpg',
                'avaliacao' => 4,
                'data' => '2023-07-22',
                'status' => 'aprovado'
            ]
        ];
        
        $stmt = $pdo->prepare("INSERT INTO depoimentos (nome, email, comentario, foto, avaliacao, data, status) VALUES (?, ?, ?, ?, ?, ?, ?)");
        
        foreach ($depoimentos as $depoimento) {
            $stmt->execute([
                $depoimento['nome'],
                $depoimento['email'],
                $depoimento['comentario'],
                $depoimento['foto'],
                $depoimento['avaliacao'],
                $depoimento['data'],
                $depoimento['status']
            ]);
        }
        
        echo "<p style='color: green;'>Depoimentos de exemplo inseridos com sucesso!</p>";
    } else {
        echo "<p>A tabela 'depoimentos' já existe.</p>";
    }
    
    echo "<p><a href='cliente.php' style='display: inline-block; padding: 10px 20px; background-color: #93622B; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px;'>Voltar para a página de clientes</a></p>";
    
} catch(PDOException $e) {
    die("<p style='color: red;'>Erro na criação da tabela: " . $e->getMessage() . "</p>");
}
?>
