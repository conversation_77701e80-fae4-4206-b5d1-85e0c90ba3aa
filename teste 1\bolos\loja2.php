<?php
include 'db.php';
session_start(); // Iniciar a sessão para verificar se o usuário está logado
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
      <!-- Font Awesome -->
     <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courgette&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Libre+Baskerville:wght@400;700&display=swap" rel="stylesheet">

     <!-- Google Fonts cake garden-->
     <link href="https://fonts.googleapis.com/css2?family=Playfair+Display&display=swap" rel="stylesheet">

     <!--H2 fonts-->
     <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lobster+Two:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
      <!-- CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Courgette&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background:#D7C5B2; ;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1, h2, h3 {
            font-family: 'Courgette', cursive;
            color: #93622B;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 36px;
        }
        
        .form-container {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 40px;
        }
        
        .form-section {
            margin-bottom: 25px;
            border-bottom: 1px solid #f0e6d9;
            padding-bottom: 20px;
        }
        
        .form-section:last-child {
            border-bottom: none;
        }
        
        .form-section h2 {
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -10px;
        }
        
        .form-group {
            flex: 1 0 200px;
            margin: 0 10px 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        select, input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            color: #333;
        }
        
        select:focus, input:focus, textarea:focus {
            outline: none;
            border-color: #93622B;
            box-shadow: 0 0 0 2px rgba(147, 98, 43, 0.2);
        }
        
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .submit-btn {
            background-color: #93622B;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 18px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            display: block;
            margin: 0 auto;
            width: 200px;
        }
        
        .submit-btn:hover {
            background-color: #7a5023;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .price-summary {
            background-color: #f9f5f0;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .price-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px dashed #e0d5c5;
        }
        
        .price-row:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: #93622B;
            margin-top: 10px;
        }
    </style>
</head>
<body>
       <!--navbar-->
        <header>
            <div class="barra">
                <div class="logo">
                <img src="../image/Cake_Garden__3_-removebg-preview.png" alt="Cake Garden Logo">
                </div>
                <!--barra de pesquisa-->
                <div class="search-container">
                    <input type="search" class="search-input" placeholder="search">
                    <button class="search-button">
                    <i class="fas fa-search"></i>
                    </button>
                </div>
                 <!--fim da barra de pesquisa-->

                    <ul class="nav-list-icon">    
                        <?php if(isset($_SESSION['user_id'])): ?>
                            <div class="user-initial">
                                <span><?php echo strtoupper(substr($_SESSION['user_name'], 0, 1)); ?></span>
                            </div>
                            <a href="logout.php" class="logout-btn">
                                <i class="fas fa-sign-out-alt"></i> sair
                            </a>
                        <?php else: ?>
                            <a href="./login.php">
                                <i class="fa-solid fa-user"></i>
                            </a>
                        <?php endif; ?>
                    </ul>

            </div>

               <!--menu-->
            <div class="menu-bar">
                <ul>
                    <li><a href="index.php" class="active"><i class="fa-solid fa-house"></i>INÍCIO</a></li>
                    <li><a href="about us.php"><i class="fa-solid fa-handshake"></i>SOBRE</a></li>
                    <li><a href="loja2.php"><i class="fa-solid fa-bag-shopping"></i>LOJA</a></li>
                    <li style="width: 150px;"><a href="contacto.php"><i class="fa-solid fa-phone"></i>CONTACTO</a></li>
                    <li><a href="blog.php"><i class="fa-solid fa-pen-to-square"></i>BLOG</a></li>
                    <li><a href="galeria.php" class="active"><i class="fa-solid fa-images"></i>GALERIA</a></li>
                    <li style="width: 150px;"><a href="cliente.php"><i class="fa-solid fa-star"></i>CLIENTE</a></li>
                </ul>
            </div>
        </header>
            <!-- fim do menu-->
<!-- Complete form with reCAPTCHA and custom fields -->
<form action="https://api.staticforms.xyz/submit" method="POST">
  <!-- Required: Your Static Forms API key -->
  <input type="hidden" name="apiKey" value="sf_9a8kg7g1ghad305n8f2i3en4">

  <!-- Enable reply-to functionality -->
  <input type="hidden" name="replyTo" value="@">

  <!-- Anti-spam honeypot field -->
  <input
    type="text"
    name="honeypot"
    style="display: none"
    tabindex="-1"
    autocomplete="off"
  >

  <!-- Form fields -->
  

  <!-- Optional redirect URLs -->
  <input type="hidden" name="redirectTo" value="https://example.com/success">



<!-- Novo formulário de pedido de bolos personalizado -->
<div class="container">
    <h1>Personalize o Seu Bolo</h1>
    
    <?php
    // Verificar se o usuário está logado
    if (!isset($_SESSION['user_id'])) {
        echo '<div class="alert alert-danger">Por favor, faça <a href="login.php">login</a> para fazer um pedido.</div>';
    } else {
        // Processar o formulário quando enviado
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['enviar_pedido'])) {
            // Obter os dados do usuário da sessão
            $id_utilizador = $_SESSION['user_id'];
            $nome = $_SESSION['user_name'];
            $email = $_SESSION['user_email'];
            
            // Obter os dados do formulário
            $tipo_bolo = isset($_POST['tipo_bolo']) ? $_POST['tipo_bolo'] : '';
            $massa = isset($_POST['massa']) ? $_POST['massa'] : '';
            $cobertura = isset($_POST['cobertura']) ? $_POST['cobertura'] : '';
            $recheio = isset($_POST['recheio']) ? $_POST['recheio'] : '';
            $peso = isset($_POST['peso']) ? $_POST['peso'] : '';
            $formato = isset($_POST['formato']) ? $_POST['formato'] : '';
            $decoracao = isset($_POST['decoracao']) ? $_POST['decoracao'] : '';
            $mensagem = isset($_POST['mensagem']) ? $_POST['mensagem'] : '';
            $data_entrega = isset($_POST['data_entrega']) ? $_POST['data_entrega'] : '';
            
            // Validar dados básicos
            if (empty($tipo_bolo) || empty($massa) || empty($peso) || empty($data_entrega)) {
                echo '<div class="alert alert-danger">Por favor, preencha todos os campos obrigatórios.</div>';
            } else {
                try {
                    // Obter informações do usuário do banco de dados
                    $stmt = $pdo->prepare("SELECT telefone FROM utilizadores WHERE id_utilizador = ?");
                    $stmt->execute([$id_utilizador]);
                    $utilizador = $stmt->fetch(PDO::FETCH_ASSOC);
                    $telefone = $utilizador['telefone'] ?? '';
                    
                    // Verificar se já existe um bolo com essas características
                    $stmt = $pdo->prepare("SELECT id_bolo FROM bolo WHERE id_tipo = ? AND id_massa = ? AND id_cobertura = ? 
                                          AND id_recheio = ? AND id_peso = ? AND id_formato = ? AND id_decoracao = ?");
                    $stmt->execute([$tipo_bolo, $massa, $cobertura, $recheio, $peso, $formato, $decoracao]);
                    $bolo_existente = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($bolo_existente) {
                        $id_bolo = $bolo_existente['id_bolo'];
                    } else {
                        // Inserir novo bolo
                        $stmt = $pdo->prepare("INSERT INTO bolo (id_tipo, id_massa, id_cobertura, id_recheio, id_peso, id_formato, id_decoracao) 
                                              VALUES (?, ?, ?, ?, ?, ?, ?)");
                        $stmt->execute([$tipo_bolo, $massa, $cobertura, $recheio, $peso, $formato, $decoracao]);
                        $id_bolo = $pdo->lastInsertId();
                    }
                    
                    // Calcular preço total
                    $preco_total = 0;

                    // Obter preço do tipo de bolo
                    $stmt_tipo = $pdo->prepare("SELECT preco FROM tipo_bolo WHERE id_tipo = ?");
                    $stmt_tipo->execute([$tipo_bolo]);
                    $preco_tipo = $stmt_tipo->fetchColumn() ?: 0;
                    $preco_total += $preco_tipo;

                    // Obter preço do peso
                    $stmt_peso = $pdo->prepare("SELECT preco FROM pesos WHERE id_peso = ?");
                    $stmt_peso->execute([$peso]);
                    $preco_peso = $stmt_peso->fetchColumn() ?: 0;
                    $preco_total += $preco_peso;

                    // Obter preço da decoração
                    if ($decoracao) {
                        $stmt_decoracao = $pdo->prepare("SELECT preco FROM decoracao WHERE id_decoracao = ?");
                        $stmt_decoracao->execute([$decoracao]);
                        $preco_decoracao = $stmt_decoracao->fetchColumn() ?: 0;
                        $preco_total += $preco_decoracao;
                    }

                    // Inserir o pedido com preço total
                    $stmt = $pdo->prepare("INSERT INTO pedidos (id_utilizador, id_bolo, data_pedido, data_entrega, mensagem, status, preco_total, status_pagamento)
                                          VALUES (?, ?, NOW(), ?, ?, 'Pendente', ?, 'Pendente')");
                    $stmt->execute([$id_utilizador, $id_bolo, $data_entrega, $mensagem, $preco_total]);

                    $pedido_id = $pdo->lastInsertId();

                    // Redirecionar para página de pagamento
                    header("Location: pagamento.php?pedido_id=" . $pedido_id);
                    exit;
                } catch (PDOException $e) {
                    echo '<div class="alert alert-danger">Erro ao processar o pedido: ' . $e->getMessage() . '</div>';
                }
            }
        }
    ?>
    
    <div class="form-container">
        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
            <!-- Removida a seção de informações pessoais -->
            
            <div class="form-section">
                <h2>Personalize seu Bolo</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="tipo_bolo">Tipo de Bolo*</label>
                        <select id="tipo_bolo" name="tipo_bolo" required>
                            <option value="">Selecione...</option>
                            <?php
                            $stmt = $pdo->query("SELECT id_tipo, nome_tipo, preco FROM tipo_bolo");
                            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                echo '<option value="' . $row['id_tipo'] . '" data-preco="' . $row['preco'] . '">' . 
                                     htmlspecialchars($row['nome_tipo']) . ' - €' . number_format($row['preco'], 2, ',', '.') . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="massa">Massa*</label>
                        <select id="massa" name="massa" required>
                            <option value="">Selecione...</option>
                            <?php
                            $stmt = $pdo->query("SELECT id_massa, nome FROM massas");
                            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                echo '<option value="' . $row['id_massa'] . '">' . htmlspecialchars($row['nome']) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="cobertura">Cobertura</label>
                        <select id="cobertura" name="cobertura">
                            <option value="">Selecione...</option>
                            <?php
                            $stmt = $pdo->query("SELECT id_cobertura, nome FROM coberturas");
                            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                echo '<option value="' . $row['id_cobertura'] . '">' . htmlspecialchars($row['nome']) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="recheio">Recheio</label>
                        <select id="recheio" name="recheio">
                            <option value="">Selecione...</option>
                            <?php
                            $stmt = $pdo->query("SELECT id_recheio, nome FROM recheios");
                            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                echo '<option value="' . $row['id_recheio'] . '">' . htmlspecialchars($row['nome']) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="peso">Peso*</label>
                        <select id="peso" name="peso" required>
                            <option value="">Selecione...</option>
                            <?php
                            $stmt = $pdo->query("SELECT id_peso, peso, preco FROM pesos");
                            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                echo '<option value="' . $row['id_peso'] . '" data-preco="' . $row['preco'] . '">' . 
                                     htmlspecialchars($row['peso']) . ' kg - €' . number_format($row['preco'], 2, ',', '.') . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="formato">Formato</label>
                        <select id="formato" name="formato">
                            <option value="">Selecione...</option>
                            <?php
                            $stmt = $pdo->query("SELECT id_formato, nome FROM formatos");
                            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                echo '<option value="' . $row['id_formato'] . '">' . htmlspecialchars($row['nome']) . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="decoracao">Decoração</label>
                        <select id="decoracao" name="decoracao">
                            <option value="">Selecione...</option>
                            <?php
                            $stmt = $pdo->query("SELECT id_decoracao, nome_decoracao, preco FROM decoracao");
                            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                echo '<option value="' . $row['id_decoracao'] . '" data-preco="' . $row['preco'] . '">' . 
                                     htmlspecialchars($row['nome_decoracao']) . ' - €' . number_format($row['preco'], 2, ',', '.') . '</option>';
                            }
                            ?>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="form-section">
                <h2>Detalhes do Pedido</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="data_entrega">Data de Entrega*</label>
                        <input type="date" id="data_entrega" name="data_entrega" required min="<?php echo date('Y-m-d', strtotime('+2 days')); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="mensagem">Mensagem ou Instruções Especiais</label>
                        <textarea id="mensagem" name="mensagem" placeholder="Alguma instrução especial para o seu bolo?"></textarea>
                    </div>
                </div>
                
                <div class="price-summary">
                    <h3>Resumo do Pedido</h3>
                    <div class="price-row">
                        <span>Tipo de Bolo:</span>
                        <span id="tipo_bolo_preco">€0,00</span>
                    </div>
                    <div class="price-row">
                        <span>Peso:</span>
                        <span id="peso_preco">€0,00</span>
                    </div>
                    <div class="price-row">
                        <span>Decoração:</span>
                        <span id="decoracao_preco">€0,00</span>
                    </div>
                    <div class="price-row">
                        <span>Total:</span>
                        <span id="preco_total">€0,00</span>
                    </div>
                </div>
            </div>
            
            <button type="submit" name="enviar_pedido" class="submit-btn">Enviar Pedido</button>
        </form>
    </div>
    <?php } // Fechamento do else (usuário logado) ?>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Definir data mínima para entrega (2 dias a partir de hoje)
        var today = new Date();
        today.setDate(today.getDate() + 2);
        var dd = String(today.getDate()).padStart(2, '0');
        var mm = String(today.getMonth() + 1).padStart(2, '0');
        var yyyy = today.getFullYear();
        var minDate = yyyy + '-' + mm + '-' + dd;
        
        document.getElementById('data_entrega').setAttribute('min', minDate);
        
        // Função para calcular o preço total
        function calcularPrecoTotal() {
            var precoTipoBolo = 0;
            var precoPeso = 0;
            var precoDecoracao = 0;
            
            // Obter preço do tipo de bolo
            var tipoBolo = document.getElementById('tipo_bolo');
            if (tipoBolo.value) {
                var opcaoSelecionada = tipoBolo.options[tipoBolo.selectedIndex];
                precoTipoBolo = parseFloat(opcaoSelecionada.getAttribute('data-preco')) || 0;
            }
            
            // Obter preço do peso
            var peso = document.getElementById('peso');
            if (peso.value) {
                var opcaoSelecionada = peso.options[peso.selectedIndex];
                precoPeso = parseFloat(opcaoSelecionada.getAttribute('data-preco')) || 0;
            }
            
            // Obter preço da decoração
            var decoracao = document.getElementById('decoracao');
            if (decoracao.value) {
                var opcaoSelecionada = decoracao.options[decoracao.selectedIndex];
                precoDecoracao = parseFloat(opcaoSelecionada.getAttribute('data-preco')) || 0;
            }
            
            // Atualizar os preços no resumo
            document.getElementById('tipo_bolo_preco').textContent = '€' + precoTipoBolo.toFixed(2).replace('.', ',');
            document.getElementById('peso_preco').textContent = '€' + precoPeso.toFixed(2).replace('.', ',');
            document.getElementById('decoracao_preco').textContent = '€' + precoDecoracao.toFixed(2).replace('.', ',');
            
            // Calcular e atualizar o preço total
            var precoTotal = precoTipoBolo + precoPeso + precoDecoracao;
            document.getElementById('preco_total').textContent = '€' + precoTotal.toFixed(2).replace('.', ',');
        }
        
        // Adicionar event listeners para recalcular o preço quando as opções mudam
        document.getElementById('tipo_bolo').addEventListener('change', calcularPrecoTotal);
        document.getElementById('peso').addEventListener('change', calcularPrecoTotal);
        document.getElementById('decoracao').addEventListener('change', calcularPrecoTotal);
        
        // Calcular o preço inicial
        calcularPrecoTotal();
    });
</script>
 <!--footer-->
        <footer>
    <div class="footer_content">
        <!-- Seção de contato -->
        <div class="footer_section footer_contactos">
            <h3><i class="fa-solid fa-phone"></i> CONTACTOS</h3>
            <ul>
                <li><a href="#"><i class="fa-solid fa-mobile"></i> +351 912 345 678</a></li>
                <li><a href="#"><i class="fa-solid fa-envelope"></i> <EMAIL></a></li>
                <li><a href="#"><i class="fa-solid fa-location-dot"></i> Av. da Liberdade, 123, Lisboa</a></li>
            </ul>
        </div>

        <!-- Seção de entrega -->
        <div class="footer_section footer_entrega">
            <h3><i class="fa-solid fa-truck"></i> ENTREGA RÁPIDA</h3>

                 <ul>
                    <li><a href="prazo_entrega.php">Prazo de entrega e Áreas atendidas</a></li>
                </ul>

        </div>

        <!-- Seção de políticas -->
        <div class="footer_section footer_politicas">
            <h3><i class="fa-solid fa-shield-halved"></i> POLÍTICAS</h3>
            <ul>
                <li><a href="politica_privacidade.php">Política de Privacidade</a></li>
                <li><a href="termos_uso.php">Termos de Uso</a></li>
                <li><a href="politica_cookies.php">Política de Cookies</a></li>
            </ul>
        </div>

        <!-- Seção de redes sociais -->
        <div class="footer_section footer_social">
            <h3><i class="fa-solid fa-globe"></i> SOCIAL</h3>
            <ul>
                <li><a href="#"><i class="fa-brands fa-facebook"></i></a></li>
                <li><a href="#"><i class="fa-brands fa-instagram"></i></a></li>
                <li><a href="#"><i class="fa-brands fa-twitter"></i></a></li>
            </ul>
        </div>
        </div>

    <!-- Copyright -->
    <div class="footer_copyright">
        &copy; 2025 Cake Garden. Todos os direitos reservados.
    </div>
</footer>
</body>
  
