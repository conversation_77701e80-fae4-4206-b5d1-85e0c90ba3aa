<?php
/**
 * Webhook do Stripe para confirmar pagamentos
 * Este endpoint recebe notificações do Stripe sobre o status dos pagamentos
 */

require_once 'vendor/autoload.php';
include 'db.php';

// Configurar logs
ini_set('log_errors', 1);
ini_set('error_log', 'stripe_webhook.log');

// Obter configurações do Stripe
try {
    $stmt = $pdo->prepare("SELECT configuracoes FROM configuracoes_pagamento WHERE metodo = 'stripe' AND ativo = TRUE");
    $stmt->execute();
    $config_row = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$config_row) {
        http_response_code(400);
        exit('Stripe não configurado');
    }
    
    $config = json_decode($config_row['configuracoes'], true);
    $webhook_secret = $config['webhook_secret'];
    
    if (empty($webhook_secret)) {
        http_response_code(400);
        exit('Webhook secret não configurado');
    }
    
} catch (Exception $e) {
    error_log('Erro ao obter configurações Stripe: ' . $e->getMessage());
    http_response_code(500);
    exit('Erro interno');
}

// Obter payload e assinatura
$payload = @file_get_contents('php://input');
$sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'] ?? '';

try {
    // Verificar assinatura do webhook
    $event = \Stripe\Webhook::constructEvent($payload, $sig_header, $webhook_secret);
    
    // Log do evento recebido
    error_log('Webhook Stripe recebido: ' . $event->type . ' - ID: ' . $event->id);
    
    // Processar evento baseado no tipo
    switch ($event->type) {
        case 'payment_intent.succeeded':
            handlePaymentSucceeded($event->data->object);
            break;
            
        case 'payment_intent.payment_failed':
            handlePaymentFailed($event->data->object);
            break;
            
        case 'payment_intent.canceled':
            handlePaymentCanceled($event->data->object);
            break;
            
        default:
            error_log('Tipo de evento não tratado: ' . $event->type);
    }
    
    http_response_code(200);
    echo 'Webhook processado com sucesso';
    
} catch (\UnexpectedValueException $e) {
    error_log('Payload inválido: ' . $e->getMessage());
    http_response_code(400);
    exit('Payload inválido');
    
} catch (\Stripe\Exception\SignatureVerificationException $e) {
    error_log('Assinatura inválida: ' . $e->getMessage());
    http_response_code(400);
    exit('Assinatura inválida');
    
} catch (Exception $e) {
    error_log('Erro no webhook: ' . $e->getMessage());
    http_response_code(500);
    exit('Erro interno');
}

function handlePaymentSucceeded($payment_intent) {
    global $pdo;
    
    try {
        $payment_intent_id = $payment_intent->id;
        $amount_received = $payment_intent->amount_received / 100; // Converter de centavos
        
        // Obter informações do pedido a partir dos metadados
        $pedido_id = $payment_intent->metadata->pedido_id ?? null;
        $pagamento_id = $payment_intent->metadata->pagamento_id ?? null;
        
        if (!$pedido_id) {
            error_log('Pedido ID não encontrado nos metadados do payment_intent: ' . $payment_intent_id);
            return;
        }
        
        // Atualizar status do pedido
        $stmt = $pdo->prepare("
            UPDATE pedidos 
            SET status_pagamento = 'Pago', 
                referencia_pagamento = ?, 
                data_pagamento = NOW()
            WHERE id_pedido = ?
        ");
        $stmt->execute([$payment_intent_id, $pedido_id]);
        
        // Atualizar registro de pagamento se existir
        if ($pagamento_id) {
            $stmt = $pdo->prepare("
                UPDATE pagamentos 
                SET status_pagamento = 'Pago', 
                    referencia_externa = ?,
                    dados_pagamento = ?
                WHERE id_pagamento = ?
            ");
            $stmt->execute([
                $payment_intent_id,
                json_encode([
                    'payment_intent_id' => $payment_intent_id,
                    'amount_received' => $amount_received,
                    'currency' => $payment_intent->currency,
                    'payment_method' => $payment_intent->payment_method ?? null,
                    'webhook_processed_at' => date('Y-m-d H:i:s')
                ]),
                $pagamento_id
            ]);
        }
        
        // Log de sucesso
        error_log("Pagamento confirmado - Pedido: $pedido_id, Payment Intent: $payment_intent_id, Valor: €$amount_received");
        
        // Aqui você pode adicionar lógica adicional como:
        // - Enviar email de confirmação
        // - Notificar admin
        // - Atualizar stock
        // - Integrar com sistema de entrega
        
    } catch (Exception $e) {
        error_log('Erro ao processar pagamento bem-sucedido: ' . $e->getMessage());
    }
}

function handlePaymentFailed($payment_intent) {
    global $pdo;
    
    try {
        $payment_intent_id = $payment_intent->id;
        $pedido_id = $payment_intent->metadata->pedido_id ?? null;
        $pagamento_id = $payment_intent->metadata->pagamento_id ?? null;
        
        if (!$pedido_id) {
            error_log('Pedido ID não encontrado nos metadados do payment_intent falhado: ' . $payment_intent_id);
            return;
        }
        
        // Atualizar status do pedido
        $stmt = $pdo->prepare("
            UPDATE pedidos 
            SET status_pagamento = 'Falhado'
            WHERE id_pedido = ?
        ");
        $stmt->execute([$pedido_id]);
        
        // Atualizar registro de pagamento se existir
        if ($pagamento_id) {
            $stmt = $pdo->prepare("
                UPDATE pagamentos 
                SET status_pagamento = 'Falhado',
                    referencia_externa = ?,
                    dados_pagamento = ?
                WHERE id_pagamento = ?
            ");
            $stmt->execute([
                $payment_intent_id,
                json_encode([
                    'payment_intent_id' => $payment_intent_id,
                    'failure_code' => $payment_intent->last_payment_error->code ?? null,
                    'failure_message' => $payment_intent->last_payment_error->message ?? null,
                    'webhook_processed_at' => date('Y-m-d H:i:s')
                ]),
                $pagamento_id
            ]);
        }
        
        error_log("Pagamento falhado - Pedido: $pedido_id, Payment Intent: $payment_intent_id");
        
    } catch (Exception $e) {
        error_log('Erro ao processar pagamento falhado: ' . $e->getMessage());
    }
}

function handlePaymentCanceled($payment_intent) {
    global $pdo;
    
    try {
        $payment_intent_id = $payment_intent->id;
        $pedido_id = $payment_intent->metadata->pedido_id ?? null;
        $pagamento_id = $payment_intent->metadata->pagamento_id ?? null;
        
        if (!$pedido_id) {
            error_log('Pedido ID não encontrado nos metadados do payment_intent cancelado: ' . $payment_intent_id);
            return;
        }
        
        // Atualizar status do pedido
        $stmt = $pdo->prepare("
            UPDATE pedidos 
            SET status_pagamento = 'Pendente'
            WHERE id_pedido = ?
        ");
        $stmt->execute([$pedido_id]);
        
        // Atualizar registro de pagamento se existir
        if ($pagamento_id) {
            $stmt = $pdo->prepare("
                UPDATE pagamentos 
                SET status_pagamento = 'Pendente',
                    referencia_externa = ?,
                    dados_pagamento = ?
                WHERE id_pagamento = ?
            ");
            $stmt->execute([
                $payment_intent_id,
                json_encode([
                    'payment_intent_id' => $payment_intent_id,
                    'canceled_at' => $payment_intent->canceled_at,
                    'cancellation_reason' => $payment_intent->cancellation_reason ?? null,
                    'webhook_processed_at' => date('Y-m-d H:i:s')
                ]),
                $pagamento_id
            ]);
        }
        
        error_log("Pagamento cancelado - Pedido: $pedido_id, Payment Intent: $payment_intent_id");
        
    } catch (Exception $e) {
        error_log('Erro ao processar pagamento cancelado: ' . $e->getMessage());
    }
}
?>
