<?php
session_start();
include 'db.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

// Verificar se foi passado um ID de pedido
$pedido_id = $_GET['pedido_id'] ?? null;
if (!$pedido_id) {
    echo "<div style='padding: 20px; background: #f8d7da; color: #721c24; margin: 20px; border-radius: 10px;'>";
    echo "<h3>❌ Erro: ID do pedido não encontrado</h3>";
    echo "<p><a href='loja2.php'>Voltar para fazer pedido</a></p>";
    echo "</div>";
    exit;
}

$user_id = $_SESSION['user_id'];

// Buscar informações do pedido
try {
    $stmt = $pdo->prepare("
        SELECT p.*, u.nome as cliente_nome, u.email as cliente_email,
               tb.nome_tipo, tb.preco as tipo_preco
        FROM pedidos p
        JOIN utilizadores u ON p.id_utilizador = u.id_utilizador
        JOIN bolo b ON p.id_bolo = b.id_bolo
        JOIN tipo_bolo tb ON b.id_tipo = tb.id_tipo
        WHERE p.id_pedido = ? AND p.id_utilizador = ?
    ");
    $stmt->execute([$pedido_id, $user_id]);
    $pedido = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$pedido) {
        echo "<div style='padding: 20px; background: #f8d7da; color: #721c24; margin: 20px; border-radius: 10px;'>";
        echo "<h3>❌ Pedido não encontrado</h3>";
        echo "<p>Pedido ID: $pedido_id</p>";
        echo "<p>Usuário ID: $user_id</p>";
        echo "<p><a href='loja2.php'>Voltar para fazer pedido</a></p>";
        echo "</div>";
        exit;
    }
    
} catch (PDOException $e) {
    die("Erro ao buscar pedido: " . $e->getMessage());
}

// Processar pagamento simulado
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['metodo_pagamento'])) {
    $metodo = $_POST['metodo_pagamento'];
    
    try {
        // Simular processamento de pagamento
        $stmt = $pdo->prepare("
            UPDATE pedidos 
            SET status_pagamento = 'Pago', 
                metodo_pagamento = ?, 
                referencia_pagamento = ?,
                data_pagamento = NOW()
            WHERE id_pedido = ?
        ");
        $referencia = 'TESTE_' . $metodo . '_' . time();
        $stmt->execute([$metodo, $referencia, $pedido_id]);
        
        // Redirecionar para página de sucesso
        header("Location: pedido_sucesso.php?pedido_id=" . $pedido_id);
        exit;
        
    } catch (PDOException $e) {
        $erro_pagamento = "Erro ao processar pagamento: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagamento - Cake Garden</title>
    <link rel="stylesheet" href="../css/estilo.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .payment-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .order-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .order-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        .order-item:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 1.2em;
            color: #93622B;
        }
        .payment-methods {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }
        .payment-method {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .payment-method:hover, .payment-method.selected {
            border-color: #93622B;
            background: #f8f5f0;
        }
        .payment-method input[type="radio"] {
            margin: 0;
        }
        .payment-method-info {
            flex: 1;
        }
        .payment-method-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .payment-method-desc {
            color: #666;
            font-size: 0.9em;
        }
        .btn-pay {
            background: #93622B;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s;
        }
        .btn-pay:hover {
            background: #7a4f23;
        }
        .btn-pay:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <h1><i class="fas fa-credit-card"></i> Finalizar Pagamento</h1>
        
        <?php if (isset($erro_pagamento)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> <?php echo $erro_pagamento; ?>
            </div>
        <?php endif; ?>
        
        <!-- Resumo do Pedido -->
        <div class="order-summary">
            <h2>Resumo do Pedido #<?php echo $pedido['id_pedido']; ?></h2>
            <div class="order-item">
                <span>Tipo de Bolo: <?php echo htmlspecialchars($pedido['nome_tipo']); ?></span>
                <span>€<?php echo number_format($pedido['tipo_preco'] ?? 0, 2, ',', '.'); ?></span>
            </div>
            <div class="order-item">
                <span>Total a Pagar</span>
                <span>€<?php echo number_format($pedido['preco_total'] ?? 0, 2, ',', '.'); ?></span>
            </div>
        </div>

        <!-- Métodos de Pagamento -->
        <h2>Escolha o Método de Pagamento</h2>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <strong>Modo de Teste:</strong> Este é um sistema de pagamento em modo de teste. 
            Nenhum pagamento real será processado.
        </div>
        
        <form method="POST">
            <div class="payment-methods">
                <div class="payment-method" onclick="selectPaymentMethod('stripe')">
                    <input type="radio" name="metodo_pagamento" value="stripe" id="stripe">
                    <div class="payment-method-info">
                        <div class="payment-method-title">
                            <i class="fab fa-stripe"></i>
                            Stripe (Cartão de Crédito/Débito)
                        </div>
                        <div class="payment-method-desc">
                            Pagamento seguro com cartão de crédito ou débito
                        </div>
                    </div>
                </div>
                
                <div class="payment-method" onclick="selectPaymentMethod('paypal')">
                    <input type="radio" name="metodo_pagamento" value="paypal" id="paypal">
                    <div class="payment-method-info">
                        <div class="payment-method-title">
                            <i class="fab fa-paypal"></i>
                            PayPal
                        </div>
                        <div class="payment-method-desc">
                            Pague com sua conta PayPal ou cartão
                        </div>
                    </div>
                </div>
                
                <div class="payment-method" onclick="selectPaymentMethod('multibanco')">
                    <input type="radio" name="metodo_pagamento" value="multibanco" id="multibanco">
                    <div class="payment-method-info">
                        <div class="payment-method-title">
                            <i class="fas fa-credit-card"></i>
                            Multibanco
                        </div>
                        <div class="payment-method-desc">
                            Pagamento por referência Multibanco
                        </div>
                    </div>
                </div>
            </div>

            <button type="submit" class="btn-pay" id="pay-button" disabled>
                <i class="fas fa-lock"></i> Pagar €<?php echo number_format($pedido['preco_total'] ?? 0, 2, ',', '.'); ?>
            </button>
        </form>

        <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; margin-top: 20px; text-align: center;">
            <i class="fas fa-shield-alt" style="color: #28a745; margin-right: 10px;"></i>
            Seus dados de pagamento são protegidos com criptografia SSL de 256 bits
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <a href="loja2.php" style="color: #93622B;">← Voltar para fazer outro pedido</a>
        </div>
    </div>

    <script>
        function selectPaymentMethod(method) {
            // Marcar o método selecionado
            document.getElementById(method).checked = true;
            
            // Remover seleção visual anterior
            document.querySelectorAll('.payment-method').forEach(el => el.classList.remove('selected'));
            
            // Adicionar seleção visual
            event.currentTarget.classList.add('selected');
            
            // Habilitar botão de pagamento
            document.getElementById('pay-button').disabled = false;
        }
    </script>
</body>
</html>
