<?php
session_start();
include '../db.php';

// Verifica se o usuário está logado e é admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    header("Location: ../login.php");
    exit;
}

$message = '';
$error = '';

// Processar formulário de configuração
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $metodo = $_POST['metodo'] ?? '';
    $ativo = isset($_POST['ativo']) ? 1 : 0;
    
    try {
        if ($metodo === 'stripe') {
            $configuracoes = [
                'public_key' => $_POST['stripe_public_key'] ?? '',
                'secret_key' => $_POST['stripe_secret_key'] ?? '',
                'webhook_secret' => $_POST['stripe_webhook_secret'] ?? ''
            ];
        } elseif ($metodo === 'paypal') {
            $configuracoes = [
                'client_id' => $_POST['paypal_client_id'] ?? '',
                'client_secret' => $_POST['paypal_client_secret'] ?? '',
                'mode' => $_POST['paypal_mode'] ?? 'sandbox'
            ];
        }
        
        // Atualizar configurações
        $stmt = $pdo->prepare("
            UPDATE configuracoes_pagamento 
            SET ativo = ?, configuracoes = ?
            WHERE metodo = ?
        ");
        $stmt->execute([$ativo, json_encode($configuracoes), $metodo]);
        
        $message = "Configurações do $metodo atualizadas com sucesso!";
        
    } catch (PDOException $e) {
        $error = "Erro ao salvar configurações: " . $e->getMessage();
    }
}

// Buscar configurações atuais
$stmt = $pdo->query("SELECT * FROM configuracoes_pagamento ORDER BY metodo");
$configuracoes = $stmt->fetchAll(PDO::FETCH_ASSOC);

$config_stripe = null;
$config_paypal = null;

foreach ($configuracoes as $config) {
    if ($config['metodo'] === 'stripe') {
        $config_stripe = $config;
    } elseif ($config['metodo'] === 'paypal') {
        $config_paypal = $config;
    }
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurações de Pagamento - Cake Garden</title>
    <link rel="stylesheet" href="../../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .config-container { padding: 20px; }
        .config-section { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .config-header { display: flex; align-items: center; gap: 15px; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 2px solid #f0f0f0; }
        .config-header h3 { margin: 0; color: #93622B; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px; }
        .form-group input:focus, .form-group select:focus { outline: none; border-color: #93622B; }
        .checkbox-group { display: flex; align-items: center; gap: 10px; }
        .checkbox-group input[type="checkbox"] { width: auto; }
        .btn-save { background: #93622B; color: white; padding: 12px 25px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        .btn-save:hover { background: #7a4f23; }
        .status-badge { padding: 5px 10px; border-radius: 15px; font-size: 12px; font-weight: bold; }
        .status-ativo { background: #d4edda; color: #155724; }
        .status-inativo { background: #f8d7da; color: #721c24; }
        .message { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .message.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .help-text { font-size: 12px; color: #666; margin-top: 5px; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="sidebar">
            <div class="logo">
                <h2>Cake Garden</h2>
                <p>Painel Admin</p>
            </div>
            <ul class="menu">
                <li><a href="dashboard.php"><i class="fa-solid fa-gauge"></i> Dashboard</a></li>
                <li><a href="bolos.php"><i class="fa-solid fa-cake-candles"></i> Gerir Bolos</a></li>
                <li><a href="pedidos.php"><i class="fa-solid fa-shopping-cart"></i> Pedidos</a></li>
                <li><a href="clientes.php"><i class="fa-solid fa-users"></i> Clientes</a></li>
                <li class="active"><a href="configuracoes_pagamento.php"><i class="fa-solid fa-credit-card"></i> Pagamentos</a></li>
                <li><a href="../index.php"><i class="fa-solid fa-home"></i> Ver Site</a></li>
                <li><a href="../logout.php"><i class="fa-solid fa-sign-out-alt"></i> Sair</a></li>
            </ul>
        </div>
        <div class="main-content">
            <div class="header">
                <h1>Configurações de Pagamento</h1>
                <div class="user-info">
                    <span>Bem-vindo, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                    <a href="../logout.php" class="logout-btn"><i class="fa-solid fa-sign-out-alt"></i></a>
                </div>
            </div>
            <div class="config-container">
                <?php if ($message): ?>
                    <div class="message success"><i class="fas fa-check-circle"></i> <?php echo $message; ?></div>
                <?php endif; ?>
                <?php if ($error): ?>
                    <div class="message error"><i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?></div>
                <?php endif; ?>

                <!-- Configurações Stripe -->
                <div class="config-section">
                    <div class="config-header">
                        <i class="fab fa-stripe fa-2x" style="color: #635bff;"></i>
                        <div>
                            <h3>Stripe</h3>
                            <span class="status-badge status-<?php echo $config_stripe && $config_stripe['ativo'] ? 'ativo' : 'inativo'; ?>">
                                <?php echo $config_stripe && $config_stripe['ativo'] ? 'Ativo' : 'Inativo'; ?>
                            </span>
                        </div>
                    </div>
                    <form method="POST">
                        <input type="hidden" name="metodo" value="stripe">
                        
                        <div class="checkbox-group">
                            <input type="checkbox" id="stripe_ativo" name="ativo" <?php echo $config_stripe && $config_stripe['ativo'] ? 'checked' : ''; ?>>
                            <label for="stripe_ativo">Ativar Stripe</label>
                        </div>
                        
                        <?php 
                        $stripe_config = $config_stripe ? json_decode($config_stripe['configuracoes'], true) : [];
                        ?>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="stripe_public_key">Chave Pública</label>
                                <input type="text" id="stripe_public_key" name="stripe_public_key" 
                                       value="<?php echo htmlspecialchars($stripe_config['public_key'] ?? ''); ?>" 
                                       placeholder="pk_test_...">
                                <div class="help-text">Chave pública do Stripe (começa com pk_)</div>
                            </div>
                            <div class="form-group">
                                <label for="stripe_secret_key">Chave Secreta</label>
                                <input type="password" id="stripe_secret_key" name="stripe_secret_key" 
                                       value="<?php echo htmlspecialchars($stripe_config['secret_key'] ?? ''); ?>" 
                                       placeholder="sk_test_...">
                                <div class="help-text">Chave secreta do Stripe (começa com sk_)</div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="stripe_webhook_secret">Webhook Secret</label>
                            <input type="password" id="stripe_webhook_secret" name="stripe_webhook_secret" 
                                   value="<?php echo htmlspecialchars($stripe_config['webhook_secret'] ?? ''); ?>" 
                                   placeholder="whsec_...">
                            <div class="help-text">Secret do webhook para verificar assinaturas</div>
                        </div>
                        
                        <button type="submit" class="btn-save">
                            <i class="fas fa-save"></i> Salvar Configurações Stripe
                        </button>
                    </form>
                </div>

                <!-- Configurações PayPal -->
                <div class="config-section">
                    <div class="config-header">
                        <i class="fab fa-paypal fa-2x" style="color: #0070ba;"></i>
                        <div>
                            <h3>PayPal</h3>
                            <span class="status-badge status-<?php echo $config_paypal && $config_paypal['ativo'] ? 'ativo' : 'inativo'; ?>">
                                <?php echo $config_paypal && $config_paypal['ativo'] ? 'Ativo' : 'Inativo'; ?>
                            </span>
                        </div>
                    </div>
                    <form method="POST">
                        <input type="hidden" name="metodo" value="paypal">
                        
                        <div class="checkbox-group">
                            <input type="checkbox" id="paypal_ativo" name="ativo" <?php echo $config_paypal && $config_paypal['ativo'] ? 'checked' : ''; ?>>
                            <label for="paypal_ativo">Ativar PayPal</label>
                        </div>
                        
                        <?php 
                        $paypal_config = $config_paypal ? json_decode($config_paypal['configuracoes'], true) : [];
                        ?>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="paypal_client_id">Client ID</label>
                                <input type="text" id="paypal_client_id" name="paypal_client_id" 
                                       value="<?php echo htmlspecialchars($paypal_config['client_id'] ?? ''); ?>" 
                                       placeholder="Client ID do PayPal">
                                <div class="help-text">Client ID da aplicação PayPal</div>
                            </div>
                            <div class="form-group">
                                <label for="paypal_client_secret">Client Secret</label>
                                <input type="password" id="paypal_client_secret" name="paypal_client_secret" 
                                       value="<?php echo htmlspecialchars($paypal_config['client_secret'] ?? ''); ?>" 
                                       placeholder="Client Secret do PayPal">
                                <div class="help-text">Client Secret da aplicação PayPal</div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="paypal_mode">Modo</label>
                            <select id="paypal_mode" name="paypal_mode">
                                <option value="sandbox" <?php echo ($paypal_config['mode'] ?? 'sandbox') === 'sandbox' ? 'selected' : ''; ?>>Sandbox (Teste)</option>
                                <option value="live" <?php echo ($paypal_config['mode'] ?? 'sandbox') === 'live' ? 'selected' : ''; ?>>Live (Produção)</option>
                            </select>
                            <div class="help-text">Use Sandbox para testes e Live para produção</div>
                        </div>
                        
                        <button type="submit" class="btn-save">
                            <i class="fas fa-save"></i> Salvar Configurações PayPal
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
