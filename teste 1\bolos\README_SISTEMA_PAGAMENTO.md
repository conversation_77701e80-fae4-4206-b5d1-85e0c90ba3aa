# Sistema de Pagamento - Cake Garden

## 📋 Visão Geral

Este sistema adiciona funcionalidades de pagamento online ao website da Cake Garden, permitindo que os clientes paguem pelos seus pedidos de bolos usando **Stripe** (cartões de crédito/débito) ou **PayPal**.

## 🚀 Funcionalidades Implementadas

### ✅ Para Clientes:
- **Cálculo automático de preços** baseado no tipo de bolo, peso e decoração
- **Página de pagamento segura** com múltiplas opções
- **Integração com Stripe** para pagamentos com cartão
- **Integração com PayPal** para pagamentos via conta PayPal
- **Confirmação de pagamento** em tempo real
- **Histórico de pedidos** com status de pagamento

### ✅ Para Administradores:
- **Painel de configuração** para Stripe e PayPal
- **Gestão de pedidos** com informações de pagamento
- **Histórico completo** de todas as transações
- **Controlo de métodos ativos/inativos**

## 📁 Ficheiros Criados/Modificados

### Novos Ficheiros:
- `pagamento.php` - Página de checkout e pagamento
- `processar_pagamento.php` - Processamento das transações
- `admin/configuracoes_pagamento.php` - Painel de configuração
- `update_payment_system.sql` - Script de atualização da BD
- `instalar_sistema_pagamento.php` - Script de instalação
- `composer.json` - Dependências PHP

### Ficheiros Modificados:
- `loja2.php` - Adicionado cálculo de preços e redirecionamento
- `admin/pedidos.php` - Adicionadas informações de pagamento
- `pedido_sucesso.php` - Confirmação de pagamento

## 🛠️ Instalação

### 1. Atualizar Base de Dados
Execute o script de instalação:
```
http://seusite.com/bolos/instalar_sistema_pagamento.php
```

### 2. Instalar Dependências PHP
No terminal, na pasta do projeto:
```bash
cd teste\ 1/bolos/
composer install
```

### 3. Configurar Métodos de Pagamento
Acesse o painel administrativo:
```
http://seusite.com/bolos/admin/configuracoes_pagamento.php
```

## 🔧 Configuração

### Stripe (Cartões de Crédito/Débito)

1. **Criar conta Stripe:**
   - Acesse: https://stripe.com/
   - Crie uma conta comercial

2. **Obter chaves API:**
   - Dashboard → Developers → API keys
   - Copie a **Publishable key** (pk_test_...)
   - Copie a **Secret key** (sk_test_...)

3. **Configurar Webhooks:**
   - Dashboard → Developers → Webhooks
   - Adicione endpoint: `https://seusite.com/bolos/webhook_stripe.php`
   - Eventos: `payment_intent.succeeded`, `payment_intent.payment_failed`

### PayPal

1. **Criar aplicação PayPal:**
   - Acesse: https://developer.paypal.com/
   - Crie uma aplicação

2. **Obter credenciais:**
   - Copie o **Client ID**
   - Copie o **Client Secret**

3. **Configurar modo:**
   - **Sandbox**: Para testes
   - **Live**: Para produção

## 🔄 Fluxo de Pagamento

1. **Cliente personaliza bolo** → Preço calculado automaticamente
2. **Submete pedido** → Redireciona para página de pagamento
3. **Escolhe método** → Stripe ou PayPal
4. **Processa pagamento** → Confirmação em tempo real
5. **Atualiza status** → Pedido marcado como "Pago"
6. **Notifica admin** → Visível no painel administrativo

## 💾 Estrutura da Base de Dados

### Tabela `pedidos` (atualizada):
```sql
ALTER TABLE pedidos ADD COLUMN preco_total DECIMAL(10,2);
ALTER TABLE pedidos ADD COLUMN status_pagamento ENUM('Pendente', 'Processando', 'Pago', 'Falhado', 'Reembolsado');
ALTER TABLE pedidos ADD COLUMN metodo_pagamento VARCHAR(50);
ALTER TABLE pedidos ADD COLUMN referencia_pagamento VARCHAR(255);
ALTER TABLE pedidos ADD COLUMN data_pagamento DATETIME;
```

### Nova Tabela `pagamentos`:
```sql
CREATE TABLE pagamentos (
    id_pagamento INT AUTO_INCREMENT PRIMARY KEY,
    id_pedido INT NOT NULL,
    valor DECIMAL(10,2) NOT NULL,
    metodo_pagamento VARCHAR(50) NOT NULL,
    status_pagamento ENUM('Pendente', 'Processando', 'Pago', 'Falhado', 'Reembolsado'),
    referencia_externa VARCHAR(255),
    dados_pagamento JSON,
    data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_pedido) REFERENCES pedidos(id_pedido)
);
```

### Nova Tabela `configuracoes_pagamento`:
```sql
CREATE TABLE configuracoes_pagamento (
    id_config INT AUTO_INCREMENT PRIMARY KEY,
    metodo VARCHAR(50) NOT NULL UNIQUE,
    ativo BOOLEAN DEFAULT FALSE,
    configuracoes JSON NOT NULL,
    data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 🧪 Testes

### Dados de Teste Stripe:
- **Cartão de sucesso**: 4242 4242 4242 4242
- **Cartão de falha**: 4000 0000 0000 0002
- **CVV**: Qualquer 3 dígitos
- **Data**: Qualquer data futura

### Dados de Teste PayPal:
- Use contas sandbox do PayPal Developer

## 🔒 Segurança

- **Chaves secretas** nunca expostas no frontend
- **Validação server-side** de todos os pagamentos
- **Webhooks** para confirmação segura
- **Logs** de todas as transações
- **Criptografia SSL** obrigatória

## 📞 Suporte

Para questões técnicas:
1. Verifique os logs de erro do servidor
2. Confirme as configurações das chaves API
3. Teste com dados de sandbox primeiro
4. Verifique a conectividade com os gateways

## 🚨 Importante

- **Sempre teste** em ambiente de desenvolvimento primeiro
- **Use HTTPS** em produção (obrigatório para pagamentos)
- **Mantenha backups** da base de dados
- **Monitore** as transações regularmente
- **Atualize** as dependências periodicamente

## 📈 Próximas Melhorias

- [ ] Relatórios financeiros detalhados
- [ ] Sistema de reembolsos automático
- [ ] Integração com MB Way
- [ ] Notificações por email automáticas
- [ ] Dashboard de vendas em tempo real
