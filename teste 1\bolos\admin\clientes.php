<?php
    include '../db.php';
    session_start();
    
    // Verifica se o usuário está logado e é admin
    if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
        header("Location: ../login.php");
        exit;
    }
    
    // Buscar todos os clientes
    $stmt = $pdo->prepare("SELECT * FROM utilizadores WHERE role = 'cliente' ORDER BY data_registo DESC");
    $stmt->execute();
    $clientes = $stmt->fetchAll();
    
    // Contar pedidos por cliente
    $pedidos_por_cliente = [];
    foreach ($clientes as $cliente) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM pedidos WHERE id_utilizador = ?");
        $stmt->execute([$cliente['id_utilizador']]);
        $pedidos_por_cliente[$cliente['id_utilizador']] = $stmt->fetch()['total'];
    }
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestão de Clientes - Cake Garden</title>
    <link rel="stylesheet" href="../../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .clientes-container {
            padding: 20px;
        }
        
        .cliente-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #93622B;
        }
        
        .cliente-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .cliente-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .stats-badge {
            background: #93622B;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .search-box {
            margin-bottom: 20px;
            padding: 10px;
            width: 100%;
            max-width: 400px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .no-clients {
            text-align: center;
            color: #666;
            padding: 40px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h2>Cake Garden</h2>
                <p>Painel Admin</p>
            </div>
            <ul class="menu">
                <li><a href="dashboard.php"><i class="fa-solid fa-gauge"></i> Dashboard</a></li>
                <li><a href="bolos.php"><i class="fa-solid fa-cake-candles"></i> Gerir Bolos</a></li>
                <li><a href="pedidos.php"><i class="fa-solid fa-shopping-cart"></i> Pedidos</a></li>
                <li class="active"><a href="clientes.php"><i class="fa-solid fa-users"></i> Clientes</a></li>
                <li><a href="../index.php"><i class="fa-solid fa-home"></i> Ver Site</a></li>
                <li><a href="../logout.php"><i class="fa-solid fa-sign-out-alt"></i> Sair</a></li>
            </ul>
        </div>

        <!-- Conteúdo principal -->
        <div class="main-content">
            <div class="header">
                <h1>Gestão de Clientes</h1>
                <div class="user-info">
                    <span>Bem-vindo, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                    <a href="../logout.php" class="logout-btn"><i class="fa-solid fa-sign-out-alt"></i></a>
                </div>
            </div>

            <div class="clientes-container">
                <input type="text" class="search-box" placeholder="Pesquisar clientes..." id="searchBox" onkeyup="filterClients()">
                
                <p><strong>Total de clientes:</strong> <?php echo count($clientes); ?></p>
                
                <?php if (empty($clientes)): ?>
                    <div class="no-clients">
                        <i class="fas fa-users" style="font-size: 48px; color: #ccc; margin-bottom: 20px;"></i>
                        <h3>Nenhum cliente cadastrado</h3>
                        <p>Os clientes aparecerão aqui quando se registarem no site.</p>
                    </div>
                <?php else: ?>
                    <div id="clientesList">
                        <?php foreach ($clientes as $cliente): ?>
                            <div class="cliente-card" data-name="<?php echo strtolower($cliente['nome']); ?>" data-email="<?php echo strtolower($cliente['email']); ?>">
                                <div class="cliente-header">
                                    <h3><i class="fas fa-user"></i> <?php echo htmlspecialchars($cliente['nome']); ?></h3>
                                    <span class="stats-badge">
                                        <?php echo $pedidos_por_cliente[$cliente['id_utilizador']]; ?> pedidos
                                    </span>
                                </div>
                                
                                <div class="cliente-info">
                                    <div>
                                        <strong><i class="fas fa-envelope"></i> Email:</strong><br>
                                        <?php echo htmlspecialchars($cliente['email']); ?>
                                    </div>
                                    <div>
                                        <strong><i class="fas fa-calendar"></i> Registado em:</strong><br>
                                        <?php echo date('d/m/Y H:i', strtotime($cliente['data_registo'])); ?>
                                    </div>
                                    <?php if (!empty($cliente['telefone'])): ?>
                                    <div>
                                        <strong><i class="fas fa-phone"></i> Telefone:</strong><br>
                                        <?php echo htmlspecialchars($cliente['telefone']); ?>
                                    </div>
                                    <?php endif; ?>
                                    <?php if (!empty($cliente['morada'])): ?>
                                    <div>
                                        <strong><i class="fas fa-map-marker-alt"></i> Morada:</strong><br>
                                        <?php echo htmlspecialchars($cliente['morada']); ?>
                                        <?php if (!empty($cliente['codigo_postal'])): ?>
                                            <br><?php echo htmlspecialchars($cliente['codigo_postal']); ?>
                                        <?php endif; ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script>
        function filterClients() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const clientCards = document.querySelectorAll('.cliente-card');
            
            clientCards.forEach(card => {
                const name = card.getAttribute('data-name');
                const email = card.getAttribute('data-email');
                
                if (name.includes(searchTerm) || email.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
