<?php
include 'db.php';
session_start();

// Verificar se o utilizador está logado
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

$message = '';

// Processar atualizações de consentimento
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $user_id = $_SESSION['user_id'];
    $marketing_consent = isset($_POST['marketing_consent']) ? 1 : 0;
    $analytics_consent = isset($_POST['analytics_consent']) ? 1 : 0;
    $functional_consent = isset($_POST['functional_consent']) ? 1 : 0;
    
    try {
        // Verificar se já existe registo de consentimento
        $stmt = $pdo->prepare("SELECT id_utilizador FROM utilizadores WHERE id_utilizador = ?");
        $stmt->execute([$user_id]);
        
        if ($stmt->fetch()) {
            // Atualizar consentimentos (assumindo que as colunas existem)
            // Nota: Estas colunas precisariam ser adicionadas à tabela utilizadores
            $stmt = $pdo->prepare("UPDATE utilizadores SET 
                marketing_consent = ?, 
                analytics_consent = ?, 
                functional_consent = ?,
                consent_updated = NOW()
                WHERE id_utilizador = ?");
            $stmt->execute([$marketing_consent, $analytics_consent, $functional_consent, $user_id]);
            
            $message = "Preferências de privacidade atualizadas com sucesso!";
        }
    } catch (PDOException $e) {
        $message = "Erro ao atualizar preferências: " . $e->getMessage();
    }
}

// Obter consentimentos atuais
$marketing_consent = false;
$analytics_consent = false;
$functional_consent = false;

try {
    $stmt = $pdo->prepare("SELECT * FROM utilizadores WHERE id_utilizador = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        // Verificar se as colunas existem antes de aceder
        $marketing_consent = isset($user['marketing_consent']) ? $user['marketing_consent'] : false;
        $analytics_consent = isset($user['analytics_consent']) ? $user['analytics_consent'] : false;
        $functional_consent = isset($user['functional_consent']) ? $user['functional_consent'] : false;
    }
} catch (PDOException $e) {
    // Se as colunas não existirem, usar valores padrão
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurações de Privacidade - Cake Garden</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courgette&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../css/style.css">
    
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background: #D7C5B2;
            color: #333;
            line-height: 1.6;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            font-family: 'Courgette', cursive;
            color: #93622B;
            text-align: center;
            font-size: 36px;
            margin-bottom: 30px;
        }
        
        h2 {
            font-family: 'Courgette', cursive;
            color: #93622B;
            font-size: 24px;
            margin-top: 30px;
            margin-bottom: 15px;
            border-bottom: 2px solid #f0e6d9;
            padding-bottom: 10px;
        }
        
        .back-btn {
            display: inline-block;
            background-color: #93622B;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-bottom: 20px;
            transition: background-color 0.3s;
        }
        
        .back-btn:hover {
            background-color: #7a5023;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .consent-section {
            background-color: #f9f5f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #e0d5c5;
        }
        
        .consent-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        
        .consent-item:last-child {
            margin-bottom: 0;
        }
        
        .consent-toggle {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 60px;
        }
        
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background-color: #ccc;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .toggle-switch.active {
            background-color: #93622B;
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s;
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }
        
        .consent-info {
            flex: 1;
        }
        
        .consent-title {
            font-weight: bold;
            color: #93622B;
            margin-bottom: 5px;
        }
        
        .consent-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .consent-details {
            font-size: 12px;
            color: #888;
        }
        
        .submit-btn {
            background-color: #93622B;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
            display: block;
            margin: 30px auto 0;
        }
        
        .submit-btn:hover {
            background-color: #7a5023;
        }
        
        .info-box {
            background-color: #e9ecef;
            border-left: 4px solid #93622B;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .data-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        
        .action-btn {
            padding: 8px 16px;
            border: 1px solid #93622B;
            background: white;
            color: #93622B;
            text-decoration: none;
            border-radius: 5px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .action-btn:hover {
            background: #93622B;
            color: white;
        }
        
        .action-btn.danger {
            border-color: #dc3545;
            color: #dc3545;
        }
        
        .action-btn.danger:hover {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <!--navbar-->
    <header>
        <div class="barra">
            <div class="logo">
                <img src="../image/Cake_Garden__3_-removebg-preview.png" alt="Cake Garden Logo">
            </div>
            <!--barra de pesquisa-->
            <div class="search-container">
                <input type="search" class="search-input" placeholder="search">
                <button class="search-button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            <!--fim da barra de pesquisa-->
            <ul class="nav-list-icon">    
                <?php if(isset($_SESSION['user_id'])): ?>
                    <div class="user-initial">
                        <span><?php echo strtoupper(substr($_SESSION['user_name'], 0, 1)); ?></span>
                    </div>
                    <a href="logout.php" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i> sair
                    </a>
                <?php else: ?>
                    <a href="./login.php">
                        <i class="fa-solid fa-user"></i>
                    </a>
                <?php endif; ?>
            </ul>
        </div>

        <!--menu-->
        <div class="menu-bar">
            <ul>
                <li><a href="index.php"><i class="fa-solid fa-house"></i>INÍCIO</a></li>
                <li><a href="about us.php"><i class="fa-solid fa-handshake"></i>SOBRE</a></li>
                <li><a href="loja2.php"><i class="fa-solid fa-bag-shopping"></i>LOJA</a></li>
                <li style="width: 150px;"><a href="contacto.php"><i class="fa-solid fa-phone"></i>CONTACTO</a></li>
                <li><a href="blog.php"><i class="fa-solid fa-pen-to-square"></i>BLOG</a></li>
                <li><a href="galeria.php"><i class="fa-solid fa-images"></i>GALERIA</a></li>
                <li style="width: 150px;"><a href="cliente.php"><i class="fa-solid fa-star"></i>CLIENTE</a></li>
            </ul>
        </div>
    </header>
    <!-- fim do menu-->

    <div class="container">
        <a href="javascript:history.back()" class="back-btn">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
        
        <h1><i class="fas fa-user-shield"></i> Configurações de Privacidade</h1>
        
        <?php if (!empty($message)): ?>
            <div class="alert <?php echo strpos($message, 'sucesso') !== false ? 'alert-success' : 'alert-danger'; ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>
        
        <div class="info-box">
            <p><strong>Controle os seus dados:</strong> Aqui pode gerir as suas preferências de privacidade e controlar como utilizamos os seus dados pessoais.</p>
        </div>

        <form method="POST" action="">
            <h2>Preferências de Consentimento</h2>
            
            <div class="consent-section">
                <div class="consent-item">
                    <div class="consent-toggle">
                        <div class="toggle-switch <?php echo $marketing_consent ? 'active' : ''; ?>" 
                             onclick="toggleConsent(this, 'marketing_consent')">
                            <div class="toggle-slider"></div>
                        </div>
                        <input type="checkbox" name="marketing_consent" id="marketing_consent" 
                               style="display: none;" <?php echo $marketing_consent ? 'checked' : ''; ?>>
                    </div>
                    <div class="consent-info">
                        <div class="consent-title">Marketing e Comunicações</div>
                        <div class="consent-description">
                            Receber newsletters, promoções e ofertas especiais por email
                        </div>
                        <div class="consent-details">
                            Pode cancelar a subscrição a qualquer momento através dos links nos emails
                        </div>
                    </div>
                </div>

                <div class="consent-item">
                    <div class="consent-toggle">
                        <div class="toggle-switch <?php echo $analytics_consent ? 'active' : ''; ?>" 
                             onclick="toggleConsent(this, 'analytics_consent')">
                            <div class="toggle-slider"></div>
                        </div>
                        <input type="checkbox" name="analytics_consent" id="analytics_consent" 
                               style="display: none;" <?php echo $analytics_consent ? 'checked' : ''; ?>>
                    </div>
                    <div class="consent-info">
                        <div class="consent-title">Análise e Estatísticas</div>
                        <div class="consent-description">
                            Permitir a recolha de dados anónimos para melhorar o site
                        </div>
                        <div class="consent-details">
                            Inclui Google Analytics e outras ferramentas de análise de tráfego
                        </div>
                    </div>
                </div>

                <div class="consent-item">
                    <div class="consent-toggle">
                        <div class="toggle-switch <?php echo $functional_consent ? 'active' : ''; ?>" 
                             onclick="toggleConsent(this, 'functional_consent')">
                            <div class="toggle-slider"></div>
                        </div>
                        <input type="checkbox" name="functional_consent" id="functional_consent" 
                               style="display: none;" <?php echo $functional_consent ? 'checked' : ''; ?>>
                    </div>
                    <div class="consent-info">
                        <div class="consent-title">Funcionalidades Avançadas</div>
                        <div class="consent-description">
                            Ativar funcionalidades personalizadas e recomendações
                        </div>
                        <div class="consent-details">
                            Lembrar preferências, histórico de navegação e sugestões personalizadas
                        </div>
                    </div>
                </div>
            </div>

            <button type="submit" class="submit-btn">
                <i class="fas fa-save"></i> Guardar Preferências
            </button>
        </form>

        <h2>Os Seus Direitos</h2>
        <div class="info-box">
            <p>De acordo com o RGPD, tem os seguintes direitos sobre os seus dados pessoais:</p>
            <div class="data-actions">
                <a href="mailto:<EMAIL>?subject=Pedido de Acesso aos Dados" class="action-btn">
                    <i class="fas fa-download"></i> Descarregar Dados
                </a>
                <a href="mailto:<EMAIL>?subject=Pedido de Retificação" class="action-btn">
                    <i class="fas fa-edit"></i> Corrigir Dados
                </a>
                <a href="mailto:<EMAIL>?subject=Pedido de Eliminação" class="action-btn danger">
                    <i class="fas fa-trash"></i> Eliminar Conta
                </a>
                <a href="politica_privacidade.php" class="action-btn">
                    <i class="fas fa-info-circle"></i> Política de Privacidade
                </a>
            </div>
        </div>

        <h2>Gestão de Cookies</h2>
        <div class="info-box">
            <p>Pode gerir as suas preferências de cookies a qualquer momento:</p>
            <div class="data-actions">
                <button onclick="CookieManager.show()" class="action-btn">
                    <i class="fas fa-cookie-bite"></i> Configurar Cookies
                </button>
                <button onclick="CookieManager.revoke()" class="action-btn danger">
                    <i class="fas fa-ban"></i> Revogar Consentimento
                </button>
                <a href="politica_cookies.php" class="action-btn">
                    <i class="fas fa-info-circle"></i> Política de Cookies
                </a>
            </div>
        </div>
    </div>

    <script>
        function toggleConsent(toggleElement, inputName) {
            const input = document.getElementById(inputName);
            const isActive = toggleElement.classList.contains('active');
            
            if (isActive) {
                toggleElement.classList.remove('active');
                input.checked = false;
            } else {
                toggleElement.classList.add('active');
                input.checked = true;
            }
        }
    </script>

    <!-- Cookie Banner Script -->
    <script src="cookie-banner.js"></script>
</body>
</html>
