<?php
    include 'db.php';
    $message = ''; // Variável para mensagens de feedback

    if ($_SERVER['REQUEST_METHOD'] === 'POST'){
        // Captura todos os campos do formulário
        $nome = $_POST['nome'] ?? '';
        $email = $_POST['email'] ?? '';
        $telefone = $_POST['telefone'] ?? '';
        $morada = $_POST['morada'] ?? '';
        $codigo_postal = $_POST['codigo_postal'] ?? '';
        $senha = $_POST['senha'] ?? '';
        $role = 'cliente'; // Por padrão, todos os registros são de clientes
        $privacy_consent = isset($_POST['privacy_consent']) ? 1 : 0;
        $marketing_consent = isset($_POST['marketing_consent']) ? 1 : 0;
        
        // Verifica se o email já existe
        $stmt = $pdo->prepare("SELECT * FROM utilizadores WHERE email = ?");
        $stmt->execute([$email]);
        $user_exists = $stmt->fetch();
        
        if ($user_exists) {
            $message = "Este email já está registrado.";
        }
        // Verifica se os campos obrigatórios foram preenchidos
        elseif (empty($nome) || empty($email) || empty($senha)) {
            $message = "Por favor, preencha todos os campos obrigatórios.";
        }
        // Verifica se o consentimento de privacidade foi dado
        elseif (!$privacy_consent) {
            $message = "É obrigatório aceitar a Política de Privacidade e os Termos de Uso.";
        } else {
            try {
                // Primeiro, vamos verificar se a coluna codigo_postal existe
                $stmt = $pdo->query("DESCRIBE utilizadores");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                $codigo_postal_exists = false;
                
                foreach ($columns as $column) {
                    if (strtolower($column) === 'codigo_postal') {
                        $codigo_postal_exists = true;
                        break;
                    }
                }
                
                // Se a coluna não existir, vamos inserir sem ela
                if (!$codigo_postal_exists) {
                    $stmt = $pdo->prepare("INSERT INTO utilizadores (nome, email, senha, telefone, morada, role) VALUES (?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $nome, 
                        $email, 
                        password_hash($senha, PASSWORD_DEFAULT), 
                        $telefone, 
                        $morada, 
                        $role
                    ]);
                } else {
                    // Se a coluna existir, incluímos ela na inserção
                    $stmt = $pdo->prepare("INSERT INTO utilizadores (nome, email, senha, telefone, morada, codigo_postal, role) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $nome, 
                        $email, 
                        password_hash($senha, PASSWORD_DEFAULT), 
                        $telefone, 
                        $morada, 
                        $codigo_postal, 
                        $role
                    ]);
                }
                
                $message = "Registo realizado com sucesso! Faça login para continuar.";
                
                // Redireciona para a página de login após 2 segundos
                header("refresh:2;url=login.php");
            } catch (PDOException $e) {
                $message = "Erro ao registrar: " . $e->getMessage();
            }
        }
    }
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registo - Cake Garden</title>
    <link rel="stylesheet" href="../css/estilo.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    
    <div class="registo">
        <div class="form-box" id="register-form">
            <form action="" method="post">
                <h2>Registo</h2>
                
                <?php if (!empty($message)): ?>
                    <p class="message <?php echo strpos($message, 'sucesso') !== false ? 'success' : 'error'; ?>">
                        <?= htmlspecialchars($message) ?>
                    </p>
                <?php endif; ?>
                
                <!-- Campo Nome -->
                <div class="input-group">
                    <i class="fa-solid fa-user"></i>
                    <input type="text" name="nome" placeholder="Nome completo" required>
                </div>
                
                <!-- Campo Email -->
                <div class="input-group">
                    <i class="fa-solid fa-envelope"></i>
                    <input type="email" name="email" placeholder="Email" required>
                </div>
                
                <!-- Campo Telefone -->
                <div class="input-group">
                    <i class="fa-solid fa-phone"></i>
                    <input type="text" name="telefone" placeholder="Telefone" required>
                </div>
                
                <!-- Campo Morada -->
                <div class="input-group">
                    <i class="fa-solid fa-home"></i>
                    <input type="text" name="morada" placeholder="Morada" required>
                </div>
                
                <!-- Campo Código Postal -->
                <div class="input-group">
                    <i class="fa-solid fa-map-pin"></i>
                    <input type="text" name="codigo_postal" placeholder="Código-Postal" required>
                </div>
                
                <!-- Campo Senha -->
                <div class="input-group">
                    <i class="fa-solid fa-lock"></i>
                    <input type="password" name="senha" placeholder="Senha" required>
                </div>

                <!-- Consentimentos -->
                <div class="consent-section">
                    <div class="consent-item">
                        <input type="checkbox" id="privacy_consent" name="privacy_consent" required>
                        <label for="privacy_consent">
                            Li e aceito a <a href="politica_privacidade.php" target="_blank">Política de Privacidade</a> e os
                            <a href="termos_uso.php" target="_blank">Termos de Uso</a> *
                        </label>
                    </div>

                    <div class="consent-item">
                        <input type="checkbox" id="marketing_consent" name="marketing_consent">
                        <label for="marketing_consent">
                            Aceito receber comunicações de marketing e promoções por email
                        </label>
                    </div>
                </div>

                <button type="submit" class="btn">Cadastrar</button>
                <p>Já tem conta? <a href="login.php">Login</a></p>
            </form>
        </div>
    </div>

    <style>
        .consent-section {
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f5f0;
            border-radius: 8px;
            border: 1px solid #e0d5c5;
        }

        .consent-item {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .consent-item:last-child {
            margin-bottom: 0;
        }

        .consent-item input[type="checkbox"] {
            margin-top: 3px;
            width: auto;
            min-width: 18px;
            height: 18px;
            accent-color: #93622B;
        }

        .consent-item label {
            font-size: 14px;
            line-height: 1.4;
            color: #555;
            cursor: pointer;
            flex: 1;
        }

        .consent-item label a {
            color: #93622B;
            text-decoration: underline;
            font-weight: 500;
        }

        .consent-item label a:hover {
            color: #7a5023;
        }

        .consent-item input[type="checkbox"]:focus {
            outline: 2px solid #93622B;
            outline-offset: 2px;
        }
    </style>
</body>

