# Políticas de Privacidade e Termos de Uso - Cake Garden

Este documento explica como implementar e utilizar as políticas de privacidade, termos de uso e gestão de cookies no site Cake Garden.

## 📋 Ficheiros Criados

### 1. Políticas Principais
- **`politica_privacidade.php`** - Política de Privacidade completa
- **`termos_uso.php`** - Termos e Condições de Uso
- **`politica_cookies.php`** - Política de Cookies detalhada

### 2. Scripts e Funcionalidades
- **`cookie-banner.js`** - Banner de consentimento de cookies
- **`README_POLITICAS.md`** - Este ficheiro de documentação

### 3. Ficheiros Atualizados
- **`loja2.php`** - Adicionada seção de políticas no footer
- **`index.php`** - Adicionada seção de políticas no footer e script de cookies
- **`registo.php`** - Adicionados checkboxes de consentimento

## 🎯 Funcionalidades Implementadas

### Política de Privacidade
- ✅ Informações sobre dados coletados
- ✅ Finalidades do tratamento de dados
- ✅ Base legal para o tratamento
- ✅ Direitos dos utilizadores (RGPD)
- ✅ Medidas de segurança
- ✅ Retenção de dados
- ✅ Contactos para exercer direitos

### Termos de Uso
- ✅ Informações da empresa
- ✅ Condições de utilização
- ✅ Processo de encomenda
- ✅ Preços e pagamento
- ✅ Cancelamentos e alterações
- ✅ Garantias de qualidade
- ✅ Informações sobre alergénios
- ✅ Limitação de responsabilidade

### Política de Cookies
- ✅ Explicação sobre cookies
- ✅ Tipos de cookies utilizados
- ✅ Cookies de terceiros
- ✅ Duração dos cookies
- ✅ Como gerir cookies
- ✅ Guias por navegador

### Banner de Cookies
- ✅ Banner responsivo
- ✅ Opções de consentimento
- ✅ Gestão de preferências
- ✅ Armazenamento de consentimento
- ✅ Animações suaves

## 🚀 Como Implementar

### 1. Verificar Ficheiros
Certifique-se de que todos os ficheiros estão na pasta correta:
```
teste 1/bolos/
├── politica_privacidade.php
├── termos_uso.php
├── politica_cookies.php
├── cookie-banner.js
└── README_POLITICAS.md
```

### 2. Adicionar Links no Footer
Os footers já foram atualizados, mas para outros ficheiros, adicione:
```html
<!-- Seção de políticas -->
<div class="footer_section footer_politicas">
    <h3><i class="fa-solid fa-shield-halved"></i> POLÍTICAS</h3>
    <ul>
        <li><a href="politica_privacidade.php">Política de Privacidade</a></li>
        <li><a href="termos_uso.php">Termos de Uso</a></li>
        <li><a href="politica_cookies.php">Política de Cookies</a></li>
    </ul>
</div>
```

### 3. Incluir Script de Cookies
Adicione antes do `</body>` em todas as páginas:
```html
<!-- Cookie Banner Script -->
<script src="cookie-banner.js"></script>
```

### 4. Formulários de Consentimento
Para formulários de registo/contacto, adicione:
```html
<div class="consent-section">
    <div class="consent-item">
        <input type="checkbox" id="privacy_consent" name="privacy_consent" required>
        <label for="privacy_consent">
            Li e aceito a <a href="politica_privacidade.php" target="_blank">Política de Privacidade</a> e os 
            <a href="termos_uso.php" target="_blank">Termos de Uso</a> *
        </label>
    </div>
    
    <div class="consent-item">
        <input type="checkbox" id="marketing_consent" name="marketing_consent">
        <label for="marketing_consent">
            Aceito receber comunicações de marketing e promoções por email
        </label>
    </div>
</div>
```

## 🎨 Personalização

### Cores e Estilos
As políticas utilizam as cores do tema Cake Garden:
- **Cor principal:** #93622B (castanho)
- **Cor secundária:** #7a5023 (castanho escuro)
- **Fundo:** #D7C5B2 (bege)
- **Destaque:** #f9f5f0 (bege claro)

### Fontes
- **Títulos:** 'Courgette' (cursiva)
- **Texto:** 'Roboto' (sans-serif)

### Responsividade
Todos os documentos são totalmente responsivos e adaptam-se a:
- 📱 Dispositivos móveis (< 768px)
- 📱 Tablets (768px - 1024px)
- 💻 Desktop (> 1024px)

## ⚙️ Configurações Avançadas

### Google Analytics
Para ativar o Google Analytics com consentimento:
1. Descomente o código no `cookie-banner.js`
2. Substitua `GA_MEASUREMENT_ID` pelo seu ID real
3. O script só carrega se o utilizador der consentimento

### Gestão de Consentimento
O script fornece funções globais:
```javascript
// Verificar estado do consentimento
CookieManager.getStatus();

// Aceitar todos os cookies
CookieManager.acceptAll();

// Aceitar apenas essenciais
CookieManager.acceptEssential();

// Revogar consentimento
CookieManager.revoke();

// Mostrar banner novamente
CookieManager.show();
```

## 📧 Contactos e Dados da Empresa

### Dados Utilizados nas Políticas
- **Nome:** Cake Garden - Doces Criações, Lda.
- **NIPC:** 123456789 (exemplo)
- **Morada:** Av. da Liberdade, 123, 1250-096 Lisboa
- **Email:** <EMAIL>
- **Telefone:** +351 912 345 678

### ⚠️ IMPORTANTE: Atualizar Dados Reais
Antes de usar em produção, atualize:
1. Dados da empresa (NIPC, morada, contactos)
2. Email do DPO (Data Protection Officer)
3. Políticas específicas da empresa
4. IDs do Google Analytics
5. Domínios e URLs reais

## 🔒 Conformidade Legal

### RGPD (Regulamento Geral de Proteção de Dados)
- ✅ Base legal para tratamento
- ✅ Direitos dos titulares
- ✅ Informação sobre transferências
- ✅ Contacto do DPO
- ✅ Direito de reclamação

### Lei Portuguesa
- ✅ Conformidade com a Lei n.º 58/2019
- ✅ Informações sobre cookies
- ✅ Consentimento explícito
- ✅ Direito de oposição

## 🔄 Manutenção

### Atualizações Regulares
- Rever políticas a cada 6 meses
- Atualizar datas de "última revisão"
- Verificar conformidade legal
- Testar funcionalidades do banner

### Monitorização
- Verificar taxa de consentimento
- Analisar feedback dos utilizadores
- Monitorizar reclamações
- Atualizar conforme mudanças legais

## 📞 Suporte

Para questões sobre implementação ou personalização:
- Consulte a documentação técnica
- Verifique os comentários no código
- Teste em ambiente de desenvolvimento primeiro

---

**Nota:** Este sistema foi desenvolvido para ser conforme com o RGPD e a legislação portuguesa. Recomenda-se sempre consultar um advogado especializado em proteção de dados para validação final.
