-- Script SQL para adicionar colunas de consentimento à tabela utilizadores
-- Execute este script para adicionar suporte a gestão de consentimentos

-- Verificar se a tabela utilizadores existe
-- Se não existir, este script irá falhar e deve criar a tabela primeiro

-- Adicionar colunas de consentimento
ALTER TABLE utilizadores 
ADD COLUMN IF NOT EXISTS marketing_consent TINYINT(1) DEFAULT 0 COMMENT 'Consentimento para marketing e comunicações',
ADD COLUMN IF NOT EXISTS analytics_consent TINYINT(1) DEFAULT 0 COMMENT 'Consentimento para análise e estatísticas',
ADD COLUMN IF NOT EXISTS functional_consent TINYINT(1) DEFAULT 1 COMMENT 'Consentimento para funcionalidades avançadas',
ADD COLUMN IF NOT EXISTS consent_updated DATETIME DEFAULT NULL COMMENT 'Data da última atualização de consentimentos',
ADD COLUMN IF NOT EXISTS privacy_policy_accepted DATETIME DEFAULT NULL COMMENT 'Data de aceitação da política de privacidade',
ADD COLUMN IF NOT EXISTS terms_accepted DATETIME DEFAULT NULL COMMENT 'Data de aceitação dos termos de uso';

-- Criar índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_marketing_consent ON utilizadores(marketing_consent);
CREATE INDEX IF NOT EXISTS idx_analytics_consent ON utilizadores(analytics_consent);
CREATE INDEX IF NOT EXISTS idx_consent_updated ON utilizadores(consent_updated);

-- Atualizar utilizadores existentes para terem consentimento funcional ativo por padrão
UPDATE utilizadores 
SET functional_consent = 1, 
    consent_updated = NOW() 
WHERE functional_consent IS NULL OR functional_consent = 0;

-- Criar tabela de log de consentimentos (opcional - para auditoria)
CREATE TABLE IF NOT EXISTS consent_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_utilizador INT NOT NULL,
    consent_type ENUM('marketing', 'analytics', 'functional', 'privacy_policy', 'terms') NOT NULL,
    consent_given TINYINT(1) NOT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_utilizador) REFERENCES utilizadores(id_utilizador) ON DELETE CASCADE,
    INDEX idx_user_consent (id_utilizador, consent_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- Inserir comentários nas colunas para documentação
ALTER TABLE utilizadores 
MODIFY COLUMN marketing_consent TINYINT(1) DEFAULT 0 COMMENT 'Consentimento para receber emails de marketing e promoções',
MODIFY COLUMN analytics_consent TINYINT(1) DEFAULT 0 COMMENT 'Consentimento para análise de comportamento e estatísticas do site',
MODIFY COLUMN functional_consent TINYINT(1) DEFAULT 1 COMMENT 'Consentimento para funcionalidades avançadas e personalização',
MODIFY COLUMN consent_updated DATETIME DEFAULT NULL COMMENT 'Timestamp da última atualização das preferências de consentimento',
MODIFY COLUMN privacy_policy_accepted DATETIME DEFAULT NULL COMMENT 'Timestamp de aceitação da política de privacidade',
MODIFY COLUMN terms_accepted DATETIME DEFAULT NULL COMMENT 'Timestamp de aceitação dos termos de uso';

-- Verificar a estrutura da tabela após as alterações
-- DESCRIBE utilizadores;

-- Exemplo de consultas úteis:

-- 1. Contar utilizadores que aceitaram marketing
-- SELECT COUNT(*) as users_with_marketing_consent FROM utilizadores WHERE marketing_consent = 1;

-- 2. Listar utilizadores que nunca atualizaram consentimentos
-- SELECT id_utilizador, nome, email FROM utilizadores WHERE consent_updated IS NULL;

-- 3. Utilizadores que aceitaram política de privacidade nos últimos 30 dias
-- SELECT COUNT(*) FROM utilizadores WHERE privacy_policy_accepted >= DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 4. Relatório de consentimentos por tipo
-- SELECT 
--     SUM(marketing_consent) as marketing_yes,
--     SUM(analytics_consent) as analytics_yes,
--     SUM(functional_consent) as functional_yes,
--     COUNT(*) as total_users
-- FROM utilizadores;

-- Criar vista para relatórios de consentimento
CREATE OR REPLACE VIEW consent_report AS
SELECT 
    u.id_utilizador,
    u.nome,
    u.email,
    u.marketing_consent,
    u.analytics_consent,
    u.functional_consent,
    u.consent_updated,
    u.privacy_policy_accepted,
    u.terms_accepted,
    u.data_registo,
    CASE 
        WHEN u.marketing_consent = 1 THEN 'Sim'
        ELSE 'Não'
    END as marketing_status,
    CASE 
        WHEN u.analytics_consent = 1 THEN 'Sim'
        ELSE 'Não'
    END as analytics_status,
    CASE 
        WHEN u.functional_consent = 1 THEN 'Sim'
        ELSE 'Não'
    END as functional_status
FROM utilizadores u
ORDER BY u.data_registo DESC;

-- Procedimento para registar mudanças de consentimento (opcional)
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS LogConsentChange(
    IN p_user_id INT,
    IN p_consent_type VARCHAR(20),
    IN p_consent_given TINYINT(1),
    IN p_ip_address VARCHAR(45),
    IN p_user_agent TEXT
)
BEGIN
    INSERT INTO consent_log (
        id_utilizador, 
        consent_type, 
        consent_given, 
        ip_address, 
        user_agent
    ) VALUES (
        p_user_id, 
        p_consent_type, 
        p_consent_given, 
        p_ip_address, 
        p_user_agent
    );
END //
DELIMITER ;

-- Trigger para registar automaticamente mudanças de consentimento
DELIMITER //
CREATE TRIGGER IF NOT EXISTS consent_change_log 
AFTER UPDATE ON utilizadores
FOR EACH ROW
BEGIN
    IF OLD.marketing_consent != NEW.marketing_consent THEN
        INSERT INTO consent_log (id_utilizador, consent_type, consent_given) 
        VALUES (NEW.id_utilizador, 'marketing', NEW.marketing_consent);
    END IF;
    
    IF OLD.analytics_consent != NEW.analytics_consent THEN
        INSERT INTO consent_log (id_utilizador, consent_type, consent_given) 
        VALUES (NEW.id_utilizador, 'analytics', NEW.analytics_consent);
    END IF;
    
    IF OLD.functional_consent != NEW.functional_consent THEN
        INSERT INTO consent_log (id_utilizador, consent_type, consent_given) 
        VALUES (NEW.id_utilizador, 'functional', NEW.functional_consent);
    END IF;
END //
DELIMITER ;

-- Comentários finais
-- Este script adiciona suporte completo para gestão de consentimentos RGPD
-- Inclui:
-- 1. Colunas de consentimento na tabela utilizadores
-- 2. Tabela de log para auditoria
-- 3. Vista para relatórios
-- 4. Procedimento para registar mudanças
-- 5. Trigger automático para log de mudanças
-- 
-- Para usar:
-- 1. Execute este script na sua base de dados
-- 2. Atualize o código PHP para usar as novas colunas
-- 3. Teste as funcionalidades de consentimento
-- 4. Configure relatórios de conformidade RGPD
