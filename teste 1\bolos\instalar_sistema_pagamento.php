<?php
/**
 * Script de instalação do sistema de pagamento
 * Execute este script uma vez para configurar o sistema de pagamento
 */

include 'db.php';

echo "<h1>Instalação do Sistema de Pagamento - Cake Garden</h1>";

try {
    // Verificar se as tabelas já existem
    $stmt = $pdo->query("SHOW TABLES LIKE 'pagamentos'");
    $tabela_pagamentos_existe = $stmt->rowCount() > 0;
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'configuracoes_pagamento'");
    $tabela_config_existe = $stmt->rowCount() > 0;
    
    // Verificar se a coluna preco_total existe na tabela pedidos
    $stmt = $pdo->query("SHOW COLUMNS FROM pedidos LIKE 'preco_total'");
    $coluna_preco_existe = $stmt->rowCount() > 0;
    
    echo "<h2>Status das Tabelas:</h2>";
    echo "<ul>";
    echo "<li>Tabela 'pagamentos': " . ($tabela_pagamentos_existe ? "✅ Existe" : "❌ Não existe") . "</li>";
    echo "<li>Tabela 'configuracoes_pagamento': " . ($tabela_config_existe ? "✅ Existe" : "❌ Não existe") . "</li>";
    echo "<li>Coluna 'preco_total' em pedidos: " . ($coluna_preco_existe ? "✅ Existe" : "❌ Não existe") . "</li>";
    echo "</ul>";
    
    if (!$coluna_preco_existe || !$tabela_pagamentos_existe || !$tabela_config_existe) {
        echo "<h2>Executando Atualizações da Base de Dados...</h2>";
        
        // Ler e executar o script SQL
        $sql_script = file_get_contents('update_payment_system.sql');
        
        if ($sql_script === false) {
            throw new Exception("Não foi possível ler o arquivo update_payment_system.sql");
        }
        
        // Dividir o script em comandos individuais
        $commands = array_filter(array_map('trim', explode(';', $sql_script)));
        
        foreach ($commands as $command) {
            if (!empty($command) && !preg_match('/^(--|USE|COMMIT)/', $command)) {
                try {
                    $pdo->exec($command);
                    echo "✅ Comando executado com sucesso<br>";
                } catch (PDOException $e) {
                    // Ignorar erros de colunas que já existem
                    if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                        echo "⚠️ Aviso: " . $e->getMessage() . "<br>";
                    }
                }
            }
        }
        
        echo "<h2>✅ Base de dados atualizada com sucesso!</h2>";
    } else {
        echo "<h2>✅ Base de dados já está atualizada!</h2>";
    }
    
    // Verificar se o Composer está instalado
    echo "<h2>Verificação de Dependências:</h2>";
    
    if (file_exists('vendor/autoload.php')) {
        echo "✅ Dependências do Composer já instaladas<br>";
    } else {
        echo "❌ Dependências do Composer não encontradas<br>";
        echo "<p><strong>Para instalar as dependências:</strong></p>";
        echo "<ol>";
        echo "<li>Abra o terminal na pasta do projeto</li>";
        echo "<li>Execute: <code>composer install</code></li>";
        echo "</ol>";
        echo "<p>Se não tiver o Composer instalado, baixe em: <a href='https://getcomposer.org/' target='_blank'>https://getcomposer.org/</a></p>";
    }
    
    // Verificar configurações de pagamento
    echo "<h2>Configurações de Pagamento:</h2>";
    
    $stmt = $pdo->query("SELECT metodo, ativo FROM configuracoes_pagamento");
    $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($configs)) {
        echo "❌ Nenhuma configuração de pagamento encontrada<br>";
    } else {
        foreach ($configs as $config) {
            $status = $config['ativo'] ? "✅ Ativo" : "❌ Inativo";
            echo "<li>" . ucfirst($config['metodo']) . ": $status</li>";
        }
    }
    
    echo "<p><a href='admin/configuracoes_pagamento.php'>➡️ Configurar métodos de pagamento</a></p>";
    
    // Instruções finais
    echo "<h2>Próximos Passos:</h2>";
    echo "<ol>";
    echo "<li>Configure as chaves do Stripe e/ou PayPal no painel administrativo</li>";
    echo "<li>Teste o sistema com dados de teste</li>";
    echo "<li>Ative os métodos de pagamento quando estiver pronto</li>";
    echo "</ol>";
    
    echo "<h2>URLs Importantes:</h2>";
    echo "<ul>";
    echo "<li><a href='admin/configuracoes_pagamento.php'>Configurações de Pagamento</a></li>";
    echo "<li><a href='admin/pedidos.php'>Gestão de Pedidos</a></li>";
    echo "<li><a href='loja2.php'>Página da Loja</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h2>❌ Erro durante a instalação:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>Verifique as permissões da base de dados e tente novamente.</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
    h1 { color: #93622B; }
    h2 { color: #333; border-bottom: 2px solid #93622B; padding-bottom: 5px; }
    ul, ol { line-height: 1.6; }
    code { background: #f4f4f4; padding: 2px 5px; border-radius: 3px; }
    a { color: #93622B; text-decoration: none; }
    a:hover { text-decoration: underline; }
</style>
