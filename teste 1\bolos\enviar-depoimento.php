<?php
require_once 'db.php'; // Inclui a conexão com o banco de dados
session_start();

$mensagem = '';

// Processar o formulário quando enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nome = $_POST['nome'] ?? '';
    $email = $_POST['email'] ?? '';
    $comentario = $_POST['comentario'] ?? '';
    $avaliacao = $_POST['avaliacao'] ?? 5;
    $data_atual = date('Y-m-d'); // Data atual
    
    // Validação básica
    if (empty($nome) || empty($email) || empty($comentario)) {
        $mensagem = '<div class="alert alert-danger">Por favor, preencha todos os campos obrigatórios.</div>';
    } else {
        // Upload da foto (opcional)
        $foto = '../image/cliente-default.jpg'; // Foto padrão
        
        if (isset($_FILES['foto']) && $_FILES['foto']['error'] === 0) {
            $allowed = ['jpg', 'jpeg', 'png', 'gif'];
            $filename = $_FILES['foto']['name'];
            $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            
            if (in_array($ext, $allowed)) {
                $new_filename = uniqid() . '.' . $ext;
                $upload_dir = '../image/clientes/';
                
                // Criar diretório se não existir
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }
                
                if (move_uploaded_file($_FILES['foto']['tmp_name'], $upload_dir . $new_filename)) {
                    $foto = $upload_dir . $new_filename;
                }
            }
        }
        
        try {
            // Inserir depoimento no banco de dados com a data atual
            $stmt = $pdo->prepare("INSERT INTO depoimentos (nome, email, comentario, foto, avaliacao, data, status) VALUES (?, ?, ?, ?, ?, ?, 'pendente')");
            $stmt->execute([$nome, $email, $comentario, $foto, $avaliacao, $data_atual]);
            
            $mensagem = '<div class="alert alert-success">Obrigado pelo seu depoimento! Ele será revisado e publicado em breve.</div>';
            
            // Redirecionar para a página de clientes após 2 segundos
            header("refresh:2;url=cliente.php");
        } catch (PDOException $e) {
            $mensagem = '<div class="alert alert-danger">Erro ao enviar depoimento: ' . $e->getMessage() . '</div>';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enviar Depoimento - Cake Garden</title>
    <link rel="stylesheet" href="../css/estilo.css">
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .depoimento-container {
            max-width: 800px;
            margin: 40px auto;
            padding: 30px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .depoimento-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .depoimento-header h1 {
            color: #93622B;
            font-family: 'Lobster Two', cursive;
            font-size: 36px;
            margin-bottom: 10px;
        }
        
        .depoimento-header p {
            color: #666;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: bold;
        }
        
        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        textarea.form-control {
            height: 150px;
            resize: vertical;
        }
        
        .rating-container {
            display: flex;
            flex-direction: row-reverse;
            justify-content: flex-end;
        }
        
        .rating-container input {
            display: none;
        }
        
        .rating-container label {
            cursor: pointer;
            font-size: 30px;
            color: #ddd;
            margin-right: 5px;
        }
        
        .rating-container label:before {
            content: '\f005';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
        }
        
        .rating-container input:checked ~ label {
            color: #FFD700;
        }
        
        .rating-container label:hover,
        .rating-container label:hover ~ label {
            color: #FFD700;
        }
        
        .submit-btn {
            background-color: #93622B;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
            display: block;
            margin: 0 auto;
        }
        
        .submit-btn:hover {
            background-color: #7A5023;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .back-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: #93622B;
            text-decoration: none;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="depoimento-container">
        <div class="depoimento-header">
            <h1>Compartilhe sua experiência</h1>
            <p>Adoraríamos ouvir sobre sua experiência com nossos bolos e serviços. Seu feedback é muito importante para nós!</p>
        </div>
        
        <?php if (!empty($mensagem)): ?>
            <?php echo $mensagem; ?>
        <?php endif; ?>
        
        <form action="" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="nome">Nome completo *</label>
                <input type="text" id="nome" name="nome" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="email">E-mail *</label>
                <input type="email" id="email" name="email" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="foto">Sua foto (opcional)</label>
                <input type="file" id="foto" name="foto" class="form-control">
                <small>Formatos aceitos: JPG, JPEG, PNG, GIF</small>
            </div>
            
            <div class="form-group">
                <label>Avaliação *</label>
                <div class="rating-container">
                    <input type="radio" id="star5" name="avaliacao" value="5" checked>
                    <label for="star5" title="5 estrelas"></label>
                    
                    <input type="radio" id="star4" name="avaliacao" value="4">
                    <label for="star4" title="4 estrelas"></label>
                    
                    <input type="radio" id="star3" name="avaliacao" value="3">
                    <label for="star3" title="3 estrelas"></label>
                    
                    <input type="radio" id="star2" name="avaliacao" value="2">
                    <label for="star2" title="2 estrelas"></label>
                    
                    <input type="radio" id="star1" name="avaliacao" value="1">
                    <label for="star1" title="1 estrela"></label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="comentario">Seu depoimento *</label>
                <textarea id="comentario" name="comentario" class="form-control" required></textarea>
            </div>
            
            <button type="submit" class="submit-btn">Enviar Depoimento</button>
        </form>
        
        <a href="cliente.php" class="back-link">Voltar para depoimentos</a>
    </div>
</body>
</html>
