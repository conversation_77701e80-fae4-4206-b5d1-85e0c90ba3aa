<?php

if(isset($_POST['nome']) && !empty($_POST['nome']));{

$nome= addslashes($_POST['nome']);
$email= addslashes($_POST['email']);
$mensagem= addslashes($_POST['message']);

}

$to = "<EMAIL>";
$subject = "contato - garden ";
$body = "Nome: ".$nome. "\n".
        "Email: ".$email. "\n".
        "Mensagem:" .$message."\n";

$header="From:".$email."\r\n"; // Define o remetente como o e-mail enviado pelo formulário
$header .= "Reply-To: ".$email."\r\n";// Define para onde as respostas devem ser enviadas
$header .= "Content-Type: text/plain; charset=UTF-8\r\n"; // Define o tipo de conteúdo como texto simples

if(mail($to,$subject,$body,$header)){
    echo ("E-mail enviado com sucesso!");
} else {
    echo ("Falha ao enviar o e-mail.");
}

?>