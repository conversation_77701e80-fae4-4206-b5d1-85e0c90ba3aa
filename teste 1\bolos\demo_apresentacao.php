<?php
/**
 * Página de demonstração para apresentações
 * Mostra o fluxo completo do sistema de forma controlada
 */

session_start();
include 'db.php';

// Simular login para demonstração
if (!isset($_SESSION['user_id']) && isset($_GET['demo'])) {
    $_SESSION['user_id'] = 999;
    $_SESSION['user_name'] = 'Cliente Demonstração';
    $_SESSION['user_email'] = '<EMAIL>';
    $_SESSION['user_role'] = 'cliente';
}

$step = $_GET['step'] ?? 'inicio';
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demonstração - Cake Garden</title>
    <link rel="stylesheet" href="../css/estilo.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
            min-height: 100vh;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #93622B, #D4A574);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .demo-steps {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .step-btn {
            background: white;
            color: #93622B;
            padding: 15px 25px;
            border: 2px solid #93622B;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .step-btn:hover, .step-btn.active {
            background: #93622B;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .demo-content {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .feature-highlight {
            background: linear-gradient(135deg, #f8f5f0, #fff);
            border-left: 5px solid #93622B;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        
        .price-demo {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-size: 1.5em;
            font-weight: bold;
            color: #28a745;
            margin: 20px 0;
        }
        
        .demo-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #93622B;
        }
        
        .form-group select, .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group select:focus, .form-group input:focus {
            outline: none;
            border-color: #93622B;
        }
        
        .payment-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .payment-method {
            background: white;
            border: 2px solid #ddd;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .payment-method:hover {
            border-color: #93622B;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .payment-icon {
            font-size: 3em;
            color: #93622B;
            margin-bottom: 15px;
        }
        
        .next-btn {
            background: #93622B;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 30px auto;
            text-decoration: none;
            text-align: center;
        }
        
        .next-btn:hover {
            background: #7a4f23;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #93622B, #7a4f23);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1><i class="fas fa-birthday-cake"></i> Cake Garden - Demonstração</h1>
            <p>Experiência completa do cliente em tempo real</p>
        </div>
        
        <div class="demo-steps">
            <a href="?step=inicio&demo=1" class="step-btn <?php echo $step === 'inicio' ? 'active' : ''; ?>">
                <i class="fas fa-home"></i> Início
            </a>
            <a href="?step=personalizacao&demo=1" class="step-btn <?php echo $step === 'personalizacao' ? 'active' : ''; ?>">
                <i class="fas fa-palette"></i> Personalização
            </a>
            <a href="?step=pagamento&demo=1" class="step-btn <?php echo $step === 'pagamento' ? 'active' : ''; ?>">
                <i class="fas fa-credit-card"></i> Pagamento
            </a>
            <a href="?step=admin&demo=1" class="step-btn <?php echo $step === 'admin' ? 'active' : ''; ?>">
                <i class="fas fa-cogs"></i> Painel Admin
            </a>
            <a href="?step=resultados&demo=1" class="step-btn <?php echo $step === 'resultados' ? 'active' : ''; ?>">
                <i class="fas fa-chart-line"></i> Resultados
            </a>
        </div>
        
        <div class="demo-content">
            <?php if ($step === 'inicio'): ?>
                <h2><i class="fas fa-rocket"></i> Bem-vindos à Cake Garden!</h2>
                
                <div class="feature-highlight">
                    <h3><i class="fas fa-mobile-alt"></i> Interface Responsiva</h3>
                    <p>O website adapta-se automaticamente a qualquer dispositivo - computador, tablet ou telemóvel.</p>
                </div>
                
                <div class="feature-highlight">
                    <h3><i class="fas fa-clock"></i> Disponível 24/7</h3>
                    <p>Os clientes podem fazer pedidos a qualquer hora, mesmo fora do horário comercial.</p>
                </div>
                
                <div class="feature-highlight">
                    <h3><i class="fas fa-user-check"></i> Registo Simples</h3>
                    <p>Processo de registo rápido e intuitivo - menos de 2 minutos para criar conta.</p>
                </div>
                
                <a href="?step=personalizacao&demo=1" class="next-btn">
                    <i class="fas fa-arrow-right"></i> Ver Personalização
                </a>
                
            <?php elseif ($step === 'personalizacao'): ?>
                <h2><i class="fas fa-palette"></i> Sistema de Personalização</h2>
                
                <div class="demo-form">
                    <div class="form-group">
                        <label for="tipo_bolo">Tipo de Bolo</label>
                        <select id="tipo_bolo" onchange="updatePrice()">
                            <option value="10">Bolo Simples - €10,00</option>
                            <option value="15">Bolo com Cobertura - €15,00</option>
                            <option value="25">Bolo Personalizado - €25,00</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="peso">Peso</label>
                        <select id="peso" onchange="updatePrice()">
                            <option value="1">1kg - €1,00</option>
                            <option value="2">2kg - €2,00</option>
                            <option value="3">3kg - €3,00</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="decoracao">Decoração</label>
                        <select id="decoracao" onchange="updatePrice()">
                            <option value="0">Sem decoração - €0,00</option>
                            <option value="5">Flores comestíveis - €5,00</option>
                            <option value="7">Decoração 3D - €7,50</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="data_entrega">Data de Entrega</label>
                        <input type="date" id="data_entrega" min="<?php echo date('Y-m-d', strtotime('+2 days')); ?>">
                    </div>
                </div>
                
                <div class="price-demo" id="preco_total">
                    <i class="fas fa-calculator"></i> Preço Total: €11,00
                </div>
                
                <div class="feature-highlight">
                    <h3><i class="fas fa-magic"></i> Cálculo Automático</h3>
                    <p>O preço atualiza instantaneamente conforme o cliente faz as suas escolhas!</p>
                </div>
                
                <a href="?step=pagamento&demo=1" class="next-btn">
                    <i class="fas fa-arrow-right"></i> Ir para Pagamento
                </a>
                
            <?php elseif ($step === 'pagamento'): ?>
                <h2><i class="fas fa-credit-card"></i> Sistema de Pagamentos</h2>
                
                <div class="feature-highlight">
                    <h3><i class="fas fa-shield-alt"></i> Segurança Máxima</h3>
                    <p>Criptografia SSL 256-bit e conformidade PCI DSS garantem total segurança.</p>
                </div>
                
                <div class="payment-methods">
                    <div class="payment-method">
                        <div class="payment-icon"><i class="fab fa-stripe"></i></div>
                        <h4>Stripe</h4>
                        <p>Cartões de crédito/débito</p>
                    </div>
                    
                    <div class="payment-method">
                        <div class="payment-icon"><i class="fab fa-paypal"></i></div>
                        <h4>PayPal</h4>
                        <p>Conta PayPal ou cartão</p>
                    </div>
                    
                    <div class="payment-method">
                        <div class="payment-icon"><i class="fas fa-mobile-alt"></i></div>
                        <h4>Multibanco</h4>
                        <p>Referência MB ou MB Way</p>
                    </div>
                </div>
                
                <div class="feature-highlight">
                    <h3><i class="fas fa-bolt"></i> Processamento Instantâneo</h3>
                    <p>Confirmação automática e atualização do status em tempo real.</p>
                </div>
                
                <a href="?step=admin&demo=1" class="next-btn">
                    <i class="fas fa-arrow-right"></i> Ver Painel Admin
                </a>
                
            <?php elseif ($step === 'admin'): ?>
                <h2><i class="fas fa-cogs"></i> Painel Administrativo</h2>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">47</div>
                        <div class="stat-label">Pedidos Este Mês</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number">€1,250</div>
                        <div class="stat-label">Vendas Este Mês</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number">23</div>
                        <div class="stat-label">Clientes Ativos</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number">95%</div>
                        <div class="stat-label">Satisfação</div>
                    </div>
                </div>
                
                <div class="feature-highlight">
                    <h3><i class="fas fa-list-check"></i> Gestão de Pedidos</h3>
                    <p>Visualize, aceite, rejeite ou marque pedidos como concluídos com um clique.</p>
                </div>
                
                <div class="feature-highlight">
                    <h3><i class="fas fa-users"></i> Base de Clientes</h3>
                    <p>Acesso completo aos dados dos clientes e histórico de compras.</p>
                </div>
                
                <div class="feature-highlight">
                    <h3><i class="fas fa-chart-bar"></i> Relatórios</h3>
                    <p>Análise detalhada de vendas, produtos mais populares e performance.</p>
                </div>
                
                <a href="?step=resultados&demo=1" class="next-btn">
                    <i class="fas fa-arrow-right"></i> Ver Resultados
                </a>
                
            <?php elseif ($step === 'resultados'): ?>
                <h2><i class="fas fa-chart-line"></i> Resultados Esperados</h2>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">+40%</div>
                        <div class="stat-label">Aumento nas Vendas</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number">-70%</div>
                        <div class="stat-label">Redução Tempo Atendimento</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Disponibilidade</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-number">3-6</div>
                        <div class="stat-label">Meses para ROI</div>
                    </div>
                </div>
                
                <div class="feature-highlight">
                    <h3><i class="fas fa-rocket"></i> Crescimento Sustentável</h3>
                    <p>Base sólida para expansão digital e crescimento a longo prazo.</p>
                </div>
                
                <div class="feature-highlight">
                    <h3><i class="fas fa-heart"></i> Satisfação do Cliente</h3>
                    <p>Experiência superior resulta em clientes mais satisfeitos e fiéis.</p>
                </div>
                
                <div style="text-align: center; margin-top: 40px;">
                    <h3>Pronto para Começar?</h3>
                    <a href="loja2.php" class="next-btn">
                        <i class="fas fa-shopping-cart"></i> Fazer Pedido Real
                    </a>
                    <a href="admin/configuracoes_pagamento.php" class="next-btn">
                        <i class="fas fa-cogs"></i> Configurar Sistema
                    </a>
                </div>
                
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        function updatePrice() {
            const tipoBolo = parseFloat(document.getElementById('tipo_bolo').value) || 0;
            const peso = parseFloat(document.getElementById('peso').value) || 0;
            const decoracao = parseFloat(document.getElementById('decoracao').value) || 0;
            
            const total = tipoBolo + peso + decoracao;
            
            document.getElementById('preco_total').innerHTML = 
                '<i class="fas fa-calculator"></i> Preço Total: €' + total.toFixed(2).replace('.', ',');
        }
        
        // Atualizar preço inicial
        if (document.getElementById('tipo_bolo')) {
            updatePrice();
        }
    </script>
</body>
</html>
