<?php
/**
 * Página de teste para verificar o fluxo de pagamento
 */

session_start();
include 'db.php';

echo "<h1>Teste do Fluxo de Pagamento</h1>";

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    echo "<div style='background: #fff3cd; padding: 15px; margin: 20px; border-radius: 10px;'>";
    echo "<h3>⚠️ Usuário não está logado</h3>";
    echo "<p>Para testar o fluxo de pagamento, você precisa estar logado.</p>";
    echo "<p><a href='login.php'>Fazer <PERSON>gin</a> | <a href='registo.php'>Registar</a></p>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; margin: 20px; border-radius: 10px;'>";
    echo "<h3>✅ Usuário logado</h3>";
    echo "<p>Nome: " . htmlspecialchars($_SESSION['user_name']) . "</p>";
    echo "<p>Email: " . htmlspecialchars($_SESSION['user_email']) . "</p>";
    echo "<p>ID: " . $_SESSION['user_id'] . "</p>";
    echo "</div>";
}

// Verificar se existem dados necessários
echo "<h2>Verificação de Dados</h2>";

try {
    // Verificar tipos de bolo
    $stmt = $pdo->query("SELECT COUNT(*) FROM tipo_bolo");
    $tipos_bolo = $stmt->fetchColumn();
    
    // Verificar pesos
    $stmt = $pdo->query("SELECT COUNT(*) FROM pesos");
    $pesos = $stmt->fetchColumn();
    
    // Verificar configurações de pagamento
    $stmt = $pdo->query("SELECT COUNT(*) FROM configuracoes_pagamento WHERE ativo = TRUE");
    $metodos_ativos = $stmt->fetchColumn();
    
    echo "<ul>";
    echo "<li>Tipos de bolo: $tipos_bolo " . ($tipos_bolo > 0 ? "✅" : "❌") . "</li>";
    echo "<li>Pesos disponíveis: $pesos " . ($pesos > 0 ? "✅" : "❌") . "</li>";
    echo "<li>Métodos de pagamento ativos: $metodos_ativos " . ($metodos_ativos > 0 ? "✅" : "❌") . "</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 20px; border-radius: 10px;'>";
    echo "<h3>❌ Erro na base de dados</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

// Criar pedido de teste
if (isset($_POST['criar_pedido_teste']) && isset($_SESSION['user_id'])) {
    try {
        // Obter dados para o teste
        $stmt = $pdo->query("SELECT id_tipo, preco FROM tipo_bolo LIMIT 1");
        $tipo_bolo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $pdo->query("SELECT id_peso, preco FROM pesos LIMIT 1");
        $peso = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($tipo_bolo && $peso) {
            // Criar bolo de teste
            $stmt = $pdo->prepare("INSERT INTO bolo (id_tipo, id_peso) VALUES (?, ?)");
            $stmt->execute([$tipo_bolo['id_tipo'], $peso['id_peso']]);
            $id_bolo = $pdo->lastInsertId();
            
            // Calcular preço total
            $preco_total = $tipo_bolo['preco'] + $peso['preco'];
            
            // Criar pedido de teste
            $stmt = $pdo->prepare("
                INSERT INTO pedidos (id_utilizador, id_bolo, data_pedido, data_entrega, mensagem, status, preco_total, status_pagamento) 
                VALUES (?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 3 DAY), 'Pedido de teste', 'Pendente', ?, 'Pendente')
            ");
            $stmt->execute([$_SESSION['user_id'], $id_bolo, $preco_total]);
            $pedido_id = $pdo->lastInsertId();
            
            echo "<div style='background: #d4edda; padding: 15px; margin: 20px; border-radius: 10px;'>";
            echo "<h3>✅ Pedido de teste criado!</h3>";
            echo "<p>ID do pedido: $pedido_id</p>";
            echo "<p>Preço total: €" . number_format($preco_total, 2, ',', '.') . "</p>";
            echo "<p><a href='pagamento.php?pedido_id=$pedido_id' style='background: #93622B; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Ir para Pagamento</a></p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; margin: 20px; border-radius: 10px;'>";
            echo "<h3>❌ Não foi possível criar pedido de teste</h3>";
            echo "<p>Dados insuficientes na base de dados</p>";
            echo "</div>";
        }
        
    } catch (PDOException $e) {
        echo "<div style='background: #f8d7da; padding: 15px; margin: 20px; border-radius: 10px;'>";
        echo "<h3>❌ Erro ao criar pedido de teste</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

// Mostrar pedidos existentes
echo "<h2>Pedidos Existentes</h2>";

try {
    if (isset($_SESSION['user_id'])) {
        $stmt = $pdo->prepare("
            SELECT p.*, tb.nome_tipo 
            FROM pedidos p 
            JOIN bolo b ON p.id_bolo = b.id_bolo 
            JOIN tipo_bolo tb ON b.id_tipo = tb.id_tipo 
            WHERE p.id_utilizador = ? 
            ORDER BY p.id_pedido DESC 
            LIMIT 5
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $pedidos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($pedidos)) {
            echo "<p>Nenhum pedido encontrado.</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr><th>ID</th><th>Tipo</th><th>Preço</th><th>Status</th><th>Pagamento</th><th>Ação</th></tr>";
            foreach ($pedidos as $pedido) {
                echo "<tr>";
                echo "<td>" . $pedido['id_pedido'] . "</td>";
                echo "<td>" . htmlspecialchars($pedido['nome_tipo'] ?? 'N/A') . "</td>";
                echo "<td>€" . number_format($pedido['preco_total'] ?? 0, 2, ',', '.') . "</td>";
                echo "<td>" . htmlspecialchars($pedido['status']) . "</td>";
                echo "<td>" . htmlspecialchars($pedido['status_pagamento'] ?? 'N/A') . "</td>";
                echo "<td><a href='pagamento.php?pedido_id=" . $pedido['id_pedido'] . "'>Pagar</a></td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; margin: 20px; border-radius: 10px;'>";
    echo "<h3>❌ Erro ao buscar pedidos</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

// Formulário para criar pedido de teste
if (isset($_SESSION['user_id'])) {
    echo "<h2>Criar Pedido de Teste</h2>";
    echo "<form method='POST'>";
    echo "<button type='submit' name='criar_pedido_teste' style='background: #93622B; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer;'>Criar Pedido de Teste</button>";
    echo "</form>";
}

// Links úteis
echo "<h2>Links Úteis</h2>";
echo "<ul>";
echo "<li><a href='loja2.php'>Página da Loja (Criar Pedido Normal)</a></li>";
echo "<li><a href='inicializar_pagamentos.php'>Inicializar Sistema de Pagamentos</a></li>";
echo "<li><a href='diagnostico_pagamentos.php'>Diagnóstico do Sistema</a></li>";
echo "<li><a href='admin/configuracoes_pagamento.php'>Configurações de Pagamento (Admin)</a></li>";
echo "</ul>";

// Teste direto da página de pagamento
if (isset($_GET['teste_pagamento'])) {
    echo "<h2>Teste Direto da Página de Pagamento</h2>";
    echo "<iframe src='pagamento.php?pedido_id=1&debug=1' width='100%' height='600' style='border: 1px solid #ccc; border-radius: 10px;'></iframe>";
}

echo "<p><a href='?teste_pagamento=1'>Testar página de pagamento diretamente</a></p>";
?>

<style>
    body { 
        font-family: Arial, sans-serif; 
        max-width: 1000px; 
        margin: 0 auto; 
        padding: 20px; 
        background: #f5f5f5; 
    }
    h1 { 
        color: #93622B; 
        text-align: center; 
        background: white; 
        padding: 20px; 
        border-radius: 10px; 
        box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
    }
    h2 { 
        color: #333; 
        border-bottom: 2px solid #93622B; 
        padding-bottom: 5px; 
        margin-top: 30px; 
    }
    table { 
        background: white; 
        border-radius: 5px; 
        overflow: hidden; 
    }
    th { 
        background: #93622B; 
        color: white; 
        padding: 10px; 
    }
    td { 
        padding: 8px; 
        text-align: center; 
    }
    a { 
        color: #93622B; 
        text-decoration: none; 
    }
    a:hover { 
        text-decoration: underline; 
    }
    ul { 
        line-height: 1.6; 
    }
</style>
